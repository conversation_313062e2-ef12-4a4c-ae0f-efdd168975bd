-- Test soubor pro minihru spojování kabelů
-- Spusťte tento soubor v FiveM konzoli nebo ho přidejte do svého resource

-- Základní test minihry s tlustými kabely
RegisterCommand('test_wiring', function(source, args)
    print("Spouštím test minihry spojování kabelů s tlustými kabely...")

    local success = exports['DVRP_minigame']:StartWiringMinigame({
        wire_count = 4,
        colors = "classic",  -- Výrazné klasické barvy
        snap_radius = 35,    -- Větší radius pro tlustší kabely
        scale = 1.0
    }, {
        onComplete = function(playerId)
            print("✅ Minihra byla úspěšně dokončena!")
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                args = {'WIRING', 'Minihra dokončena úspěšně!'}
            })
        end,
        
        onFail = function(playerId, reason)
            print("❌ Minihra selhala: " .. (reason or "neznámý důvod"))
            TriggerEvent('chat:addMessage', {
                color = {255, 0, 0},
                args = {'WIRING', 'Minihra selhala: ' .. (reason or "neznámý důvod")}
            })
        end,
        
        onClose = function(playerId)
            print("🚪 Minihra byla zavřena")
            TriggerEvent('chat:addMessage', {
                color = {128, 128, 128},
                args = {'WIRING', 'Minihra byla zavřena'}
            })
        end
    })
    
    if success then
        print("✅ Minihra byla spuštěna úspěšně")
    else
        print("❌ Nepodařilo se spustit minihru")
    end
end)

-- Test s více tlustými kabely
RegisterCommand('test_wiring_hard', function(source, args)
    print("Spouštím těžkou verzi minihry s tlustými kabely...")

    exports['DVRP_minigame']:StartWiringMinigame({
        wire_count = 6,
        colors = "extended",  -- Výrazné rozšířené barvy
        snap_radius = 30,     -- Větší radius pro tlustší kabely
        scale = 1.2
    })
end)

-- Test s rainbow tlustými kabely
RegisterCommand('test_wiring_rainbow', function(source, args)
    print("Spouštím rainbow verzi minihry s tlustými kabely...")

    exports['DVRP_minigame']:StartWiringMinigame({
        wire_count = 8,
        colors = "rainbow",   -- Výrazné rainbow barvy
        snap_radius = 32,     -- Větší radius pro více kabelů
        scale = 1.0
    })
end)

-- Test s extra tlustými kabely a výraznými barvami
RegisterCommand('test_wiring_thick', function(source, args)
    print("Spouštím test s extra tlustými kabely a výraznými barvami...")

    exports['DVRP_minigame']:StartWiringMinigame({
        wire_count = 4,
        colors = {"#FF0000", "#00FF00", "#0066FF", "#FF00FF"}, -- Vlastní výrazné barvy
        snap_radius = 40,     -- Velký radius pro snadné spojování
        scale = 1.1
    }, {
        onComplete = function(playerId)
            print("✅ Extra tlustá minihra dokončena!")
            TriggerEvent('chat:addMessage', {
                color = {0, 255, 0},
                args = {'THICK WIRING', 'Extra tlustá minihra dokončena! 🔌'}
            })
        end
    })
end)

print("Test příkazy zaregistrovány:")
print("- /test_wiring - základní test s tlustými kabely")
print("- /test_wiring_hard - těžká verze s tlustými kabely")
print("- /test_wiring_rainbow - rainbow verze s tlustými kabely")
print("- /test_wiring_thick - extra tlustá verze s výraznými barvami")
