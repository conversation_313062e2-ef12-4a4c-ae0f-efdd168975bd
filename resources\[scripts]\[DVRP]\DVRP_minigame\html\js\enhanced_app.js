// Enhanced mx_fixwiring Vue application
// Supports 1-32 wires with dynamic colors and positioning

// Use global Vue (compatible with existing setup)
const { gsap } = window;
const { Draggable } = window;

// Color palettes
const COLOR_PALETTES = {
    classic: ["#324d9c", "#e52320", "#ffeb13", "#a6529a"],
    extended: ["#324d9c", "#e52320", "#ffeb13", "#a6529a", "#00ff00", "#ff8800", "#8800ff", "#00ffff"],
    rainbow: [
        "#ff0000", "#ff8800", "#ffff00", "#88ff00", "#00ff00", "#00ff88", 
        "#00ffff", "#0088ff", "#0000ff", "#8800ff", "#ff00ff", "#ff0088",
        "#ff4444", "#ffaa44", "#ffff44", "#aaff44", "#44ff44", "#44ffaa",
        "#44ffff", "#44aaff", "#4444ff", "#aa44ff", "#ff44ff", "#ff44aa",
        "#880000", "#884400", "#888800", "#448800", "#008800", "#008844",
        "#008888", "#004488", "#000088", "#440088", "#880088", "#880044"
    ]
};

// Utility functions
function generateAutoColors(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
        const hue = (i * 360 / count) % 360;
        const saturation = 70 + (i % 3) * 10;
        const lightness = 45 + (i % 2) * 10;
        colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
    }
    return colors;
}

function getColors(colorConfig, wireCount) {
    if (Array.isArray(colorConfig)) {
        return colorConfig.slice(0, wireCount);
    }
    
    switch (colorConfig) {
        case "classic":
            return COLOR_PALETTES.classic.slice(0, wireCount);
        case "extended":
            return COLOR_PALETTES.extended.slice(0, wireCount);
        case "rainbow":
            return COLOR_PALETTES.rainbow.slice(0, wireCount);
        case "auto":
        default:
            if (wireCount <= 4) return COLOR_PALETTES.classic.slice(0, wireCount);
            if (wireCount <= 8) return COLOR_PALETTES.extended.slice(0, wireCount);
            if (wireCount <= 32) return COLOR_PALETTES.rainbow.slice(0, wireCount);
            return generateAutoColors(wireCount);
    }
}

function calculateWirePositions(wireCount, gameWidth = 907, gameHeight = 907) {
    const leftSide = [];
    const rightSide = [];
    
    const topMargin = 60;
    const bottomMargin = 60;
    const availableHeight = gameHeight - topMargin - bottomMargin;
    const spacing = availableHeight / (wireCount + 1);
    const wireHeight = Math.min(40, Math.max(15, availableHeight / wireCount * 0.6));
    
    for (let i = 0; i < wireCount; i++) {
        const y = topMargin + spacing * (i + 1) - wireHeight / 2;
        leftSide.push({
            x: 60,
            y: y,
            width: 60,
            height: wireHeight
        });
        rightSide.push({
            x: gameWidth - 120,
            y: y,
            width: 108,
            height: wireHeight
        });
    }
    
    return { leftSide, rightSide };
}

function generateRandomConfiguration(wireCount, colors) {
    const wires = [];
    const targetLights = [...Array(wireCount).keys()];
    
    // Shuffle for random connections
    for (let i = targetLights.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [targetLights[i], targetLights[j]] = [targetLights[j], targetLights[i]];
    }
    
    const positions = calculateWirePositions(wireCount);
    
    for (let i = 0; i < wireCount; i++) {
        const targetIndex = targetLights[i];
        wires.push({
            id: i + 1,
            color: colors[i],
            startPos: positions.leftSide[i],
            targetPos: positions.rightSide[targetIndex],
            targetLight: targetIndex + 1,
            targetX: 670,
            targetY: positions.rightSide[targetIndex].y - positions.leftSide[i].y
        });
    }
    
    return wires;
}

// Vue application (compatible with Vue 2)
const app = new Vue({
    el: '#app',
    template: `
        <div v-if="NuiOpen" :style="position_nui" id="container">
            <svg
                :style="size_game"
                :width="gameWidth"
                :height="gameHeight"
                :viewBox="\`0 0 \${gameWidth} \${gameHeight}\`"
                class="game-svg"
                :class="wireContainerClass"
            >
                <!-- Background gradient -->
                <defs>
                    <linearGradient id="bgGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                        <stop offset="0%" style="stop-color:#1d1d1b"/>
                        <stop offset="50%" style="stop-color:#272726"/>
                        <stop offset="100%" style="stop-color:#1d1d1b"/>
                    </linearGradient>

                    <!-- Wire glow effects -->
                    <filter id="wireGlow">
                        <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
                        <feMerge>
                            <feMergeNode in="coloredBlur"/>
                            <feMergeNode in="SourceGraphic"/>
                        </feMerge>
                    </filter>
                </defs>

                <!-- Background -->
                <rect width="100%" height="100%" fill="url(#bgGradient)"/>

                <!-- Circuit background pattern -->
                <g class="circuit-bg">
                    <path v-for="line in backgroundLines" :key="line.id" :d="line.path"/>
                </g>

                <!-- Left side connection points -->
                <g class="left-connections">
                    <rect
                        v-for="(pos, index) in wirePositions.leftSide"
                        :key="\`left-\${index}\`"
                        :x="pos.x - 10"
                        :y="pos.y - 5"
                        :width="pos.width + 20"
                        :height="pos.height + 10"
                        fill="#393e42"
                        stroke="#555"
                        stroke-width="2"
                        rx="5"
                    />
                </g>

                <!-- Right side connection points -->
                <g class="right-connections">
                    <rect
                        v-for="(pos, index) in wirePositions.rightSide"
                        :key="\`right-\${index}\`"
                        :x="pos.x - 10"
                        :y="pos.y - 5"
                        :width="pos.width + 20"
                        :height="pos.height + 10"
                        fill="#393e42"
                        stroke="#555"
                        stroke-width="2"
                        rx="5"
                    />
                </g>

                <!-- Wire lines -->
                <g class="wire-lines">
                    <line
                        v-for="(wire, index) in wires"
                        :key="\`line-\${wire.id}\`"
                        :class="\`wire-line line-\${wire.id}\`"
                        :x1="wire.startPos.x + 30"
                        :y1="wire.startPos.y + 20"
                        :x2="wire.startPos.x + 30"
                        :y2="wire.startPos.y + 20"
                        :stroke="wire.color"
                        stroke-width="4"
                        filter="url(#wireGlow)"
                    />
                </g>

                <!-- Target areas -->
                <g class="target-areas">
                    <rect
                        v-for="(wire, index) in wires"
                        :key="\`target-\${wire.id}\`"
                        :class="\`wire-target target-\${wire.id}\`"
                        :x="wire.targetPos.x"
                        :y="wire.targetPos.y"
                        :width="wire.targetPos.width"
                        :height="wire.targetPos.height"
                        :fill="wire.color"
                        fill-opacity="0.2"
                        rx="3"
                    />
                </g>

                <!-- Draggable wire elements -->
                <g class="draggable-wires">
                    <rect
                        v-for="(wire, index) in wires"
                        :key="\`drag-\${wire.id}\`"
                        :class="\`wire-drag drag-\${wire.id}\`"
                        :x="wire.startPos.x"
                        :y="wire.startPos.y"
                        :width="wire.startPos.width"
                        :height="wire.startPos.height"
                        :fill="wire.color"
                        stroke="#fff"
                        stroke-width="2"
                        rx="3"
                        filter="url(#wireGlow)"
                    />
                </g>

                <!-- Light indicators -->
                <g class="lights">
                    <rect
                        v-for="(wire, index) in wires"
                        :key="\`light-\${wire.id}\`"
                        :class="\`light light-\${wire.targetLight}\`"
                        :x="gameWidth - 60"
                        :y="wire.targetPos.y + 5"
                        width="50"
                        height="24"
                        fill="#ffff00"
                        opacity="0"
                        rx="12"
                        stroke="#ffd700"
                        stroke-width="2"
                    />
                </g>

                <!-- Wire count indicator -->
                <text
                    :x="gameWidth / 2"
                    y="30"
                    text-anchor="middle"
                    fill="#fff"
                    font-size="16"
                    font-family="Roboto"
                    opacity="0.7"
                >
                    {{ wire_count }} Wires - Connect matching colors
                </text>
            </svg>
        </div>
    `,
    data() {
        return {
            NuiOpen: false,
            size_game: "width: 90vmin;",
            position_nui: "left: 50%; top: 50%; transform: translate(-50%, -50%) scale(1);",
            name_resource: "",
            sound_name: "",
            wire_count: 4,
            colors: "auto",
            snap_radius: 25,
            wires: [],
            completedWires: [],
            gameWidth: 907,
            gameHeight: 907,
            draggables: []
        };
    },
    
    computed: {
        wirePositions() {
            return calculateWirePositions(this.wire_count, this.gameWidth, this.gameHeight);
        },
        
        wireColors() {
            return getColors(this.colors, this.wire_count);
        },
        
        wireContainerClass() {
            if (this.wire_count <= 4) return 'wire-container-small';
            if (this.wire_count <= 8) return 'wire-container-medium';
            if (this.wire_count <= 16) return 'wire-container-large';
            return 'wire-container-xlarge';
        },
        
        backgroundLines() {
            // Generate decorative circuit lines
            const lines = [];
            for (let i = 0; i < 5; i++) {
                lines.push({
                    id: i,
                    path: `M${100 + i * 150},50 L${100 + i * 150},${this.gameHeight - 50}`
                });
            }
            return lines;
        }
    },
    
    created() {
        window.addEventListener("message", this.receiveLua);
        window.addEventListener("keydown", this.keyPress);
        gsap.registerPlugin(Draggable);
    },

    destroyed() {
        window.removeEventListener("message", this.receiveLua);
        window.removeEventListener("keydown", this.keyPress);
        this.destroyDraggables();
    },
    
    methods: {
        SendToClient(endpoint, data) {
            fetch(`https://${this.name_resource}/${endpoint}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(data)
            }).catch(error => console.log(`Error in ${endpoint}:`, error));
        },
        
        keyPress(event) {
            if (event.key === "Escape" || event.key === "Backspace") {
                this.SendToClient("CloseNui", {});
            }
        },
        
        receiveLua(event) {
            if (event?.data?.ui === "ui") {
                const data = event.data;
                this.NuiOpen = data.NuiOpen;
                
                if (data.NuiOpen) {
                    this.position_nui = `left: ${data.x}; top: ${data.y}; transform: translate(-${data.x}, -${data.y}) scale(${data.scale});`;
                    this.size_game = `width: ${data.size_game};`;
                    this.name_resource = data.name_resource;
                    this.sound_name = data.sound_name;
                    this.wire_count = Math.min(32, Math.max(1, data.wire_count || 4));
                    this.colors = data.colors || "auto";
                    this.snap_radius = data.snap_radius || 25;
                    
                    this.$nextTick(() => {
                        setTimeout(() => this.initializeGame(), 100);
                    });
                }
            }
        },
        
        initializeGame() {
            this.destroyDraggables();
            this.wires = generateRandomConfiguration(this.wire_count, this.wireColors);
            this.completedWires = new Array(this.wire_count).fill(false);
            
            this.$nextTick(() => {
                setTimeout(() => this.setupDraggables(), 200);
            });
        },
        
        setupDraggables() {
            this.draggables = [];
            
            for (let i = 0; i < this.wire_count; i++) {
                const wireId = i + 1;
                const wire = this.wires[i];
                
                let animationFrameId = null;

                const draggable = new Draggable(`.drag-${wireId}`, {
                    onDrag: () => {
                        // Use requestAnimationFrame to throttle updates
                        if (animationFrameId) {
                            cancelAnimationFrame(animationFrameId);
                        }

                        animationFrameId = requestAnimationFrame(() => {
                            this.updateLine(wireId, draggable.x + 90, draggable.y + wire.startPos.y + 20);
                        });
                    },

                    onRelease: () => {
                        // Cancel any pending animation frame
                        if (animationFrameId) {
                            cancelAnimationFrame(animationFrameId);
                            animationFrameId = null;
                        }

                        const isCorrect = Math.abs(draggable.x - wire.targetX) < this.snap_radius &&
                                        Math.abs(draggable.y - wire.targetY) < this.snap_radius;

                        if (isCorrect) {
                            this.toggleLight(wire.targetLight, true);
                            this.completedWires[i] = true;
                            this.checkCompletion();
                        } else {
                            this.resetWire(wireId, wire);
                            this.toggleLight(wire.targetLight, false);
                            this.completedWires[i] = false;
                        }
                    },

                    liveSnap: {
                        points: [{x: wire.targetX, y: wire.targetY}],
                        radius: this.snap_radius
                    }
                });
                
                this.draggables.push(draggable);
            }
        },
        
        updateLine(wireId, x2, y2) {
            // Cache line elements for better performance
            if (!this.lineElementsCache) {
                this.lineElementsCache = {};
            }

            if (!this.lineElementsCache[wireId]) {
                this.lineElementsCache[wireId] = document.querySelector(`.line-${wireId}`);
            }

            const lineElement = this.lineElementsCache[wireId];
            if (lineElement) {
                // Direct DOM manipulation is faster than GSAP for simple attribute changes during drag
                lineElement.setAttributeNS(null, 'x2', x2);
                lineElement.setAttributeNS(null, 'y2', y2);
            }
        },
        
        toggleLight(lightId, isOn) {
            gsap.to(`.light-${lightId}`, {
                opacity: isOn ? 1 : 0,
                duration: 0.3,
                ease: "power2.out"
            });
        },
        
        resetWire(wireId, wire) {
            gsap.to(`.drag-${wireId}`, {
                duration: 0.3,
                ease: "power2.out",
                x: 0,
                y: 0
            });
            
            this.updateLine(wireId, wire.startPos.x + 30, wire.startPos.y + 20);
        },
        
        checkCompletion() {
            if (this.completedWires.every(completed => completed)) {
                if (this.sound_name) {
                    new Audio(`sound/${this.sound_name}`).play().catch(() => {});
                }
                
                setTimeout(() => {
                    for (let i = 0; i < this.wire_count; i++) {
                        this.resetWire(i + 1, this.wires[i]);
                        this.toggleLight(i + 1, false);
                    }
                    this.completedWires.fill(false);
                    this.SendToClient("electric_circuit_completed", {});
                }, 2000);
            }
        },
        
        destroyDraggables() {
            this.draggables.forEach(d => d.kill());
            this.draggables = [];
        }
    }
});
