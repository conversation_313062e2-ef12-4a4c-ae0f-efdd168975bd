{"version": 3, "sources": ["webpack:///webpack/bootstrap", "webpack:///./src/App.vue?7e02", "webpack:///./src/App.vue?dd6c", "webpack:///src/App.vue", "webpack:///./src/App.vue?1160", "webpack:///./src/App.vue?bff9", "webpack:///./src/main.js"], "names": ["webpackJsonpCallback", "data", "moduleId", "chunkId", "chunkIds", "moreModules", "executeModules", "i", "resolves", "length", "Object", "prototype", "hasOwnProperty", "call", "installedChunks", "push", "modules", "parentJsonpFunction", "shift", "deferredModules", "apply", "checkDeferredModules", "result", "deferredModule", "fulfilled", "j", "depId", "splice", "__webpack_require__", "s", "installedModules", "exports", "module", "l", "m", "c", "d", "name", "getter", "o", "defineProperty", "enumerable", "get", "r", "Symbol", "toStringTag", "value", "t", "mode", "__esModule", "ns", "create", "key", "bind", "n", "object", "property", "p", "jsonpArray", "window", "oldJsonpFunction", "slice", "_vm", "this", "_h", "$createElement", "_c", "_self", "style", "attrs", "staticStyle", "staticClass", "game_change", "_e", "staticRenderFns", "component", "<PERSON><PERSON>", "config", "productionTip", "render", "h", "App", "$mount"], "mappings": "aACE,SAASA,EAAqBC,GAQ7B,IAPA,IAMIC,EAAUC,EANVC,EAAWH,EAAK,GAChBI,EAAcJ,EAAK,GACnBK,EAAiBL,EAAK,GAIHM,EAAI,EAAGC,EAAW,GACpCD,EAAIH,EAASK,OAAQF,IACzBJ,EAAUC,EAASG,GAChBG,OAAOC,UAAUC,eAAeC,KAAKC,EAAiBX,IAAYW,EAAgBX,IACpFK,EAASO,KAAKD,EAAgBX,GAAS,IAExCW,EAAgBX,GAAW,EAE5B,IAAID,KAAYG,EACZK,OAAOC,UAAUC,eAAeC,KAAKR,EAAaH,KACpDc,EAAQd,GAAYG,EAAYH,IAG/Be,GAAqBA,EAAoBhB,GAE5C,MAAMO,EAASC,OACdD,EAASU,OAATV,GAOD,OAHAW,EAAgBJ,KAAKK,MAAMD,EAAiBb,GAAkB,IAGvDe,IAER,SAASA,IAER,IADA,IAAIC,EACIf,EAAI,EAAGA,EAAIY,EAAgBV,OAAQF,IAAK,CAG/C,IAFA,IAAIgB,EAAiBJ,EAAgBZ,GACjCiB,GAAY,EACRC,EAAI,EAAGA,EAAIF,EAAed,OAAQgB,IAAK,CAC9C,IAAIC,EAAQH,EAAeE,GACG,IAA3BX,EAAgBY,KAAcF,GAAY,GAE3CA,IACFL,EAAgBQ,OAAOpB,IAAK,GAC5Be,EAASM,EAAoBA,EAAoBC,EAAIN,EAAe,KAItE,OAAOD,EAIR,IAAIQ,EAAmB,GAKnBhB,EAAkB,CACrB,IAAO,GAGJK,EAAkB,GAGtB,SAASS,EAAoB1B,GAG5B,GAAG4B,EAAiB5B,GACnB,OAAO4B,EAAiB5B,GAAU6B,QAGnC,IAAIC,EAASF,EAAiB5B,GAAY,CACzCK,EAAGL,EACH+B,GAAG,EACHF,QAAS,IAUV,OANAf,EAAQd,GAAUW,KAAKmB,EAAOD,QAASC,EAAQA,EAAOD,QAASH,GAG/DI,EAAOC,GAAI,EAGJD,EAAOD,QAKfH,EAAoBM,EAAIlB,EAGxBY,EAAoBO,EAAIL,EAGxBF,EAAoBQ,EAAI,SAASL,EAASM,EAAMC,GAC3CV,EAAoBW,EAAER,EAASM,IAClC3B,OAAO8B,eAAeT,EAASM,EAAM,CAAEI,YAAY,EAAMC,IAAKJ,KAKhEV,EAAoBe,EAAI,SAASZ,GACX,qBAAXa,QAA0BA,OAAOC,aAC1CnC,OAAO8B,eAAeT,EAASa,OAAOC,YAAa,CAAEC,MAAO,WAE7DpC,OAAO8B,eAAeT,EAAS,aAAc,CAAEe,OAAO,KAQvDlB,EAAoBmB,EAAI,SAASD,EAAOE,GAEvC,GADU,EAAPA,IAAUF,EAAQlB,EAAoBkB,IAC/B,EAAPE,EAAU,OAAOF,EACpB,GAAW,EAAPE,GAA8B,kBAAVF,GAAsBA,GAASA,EAAMG,WAAY,OAAOH,EAChF,IAAII,EAAKxC,OAAOyC,OAAO,MAGvB,GAFAvB,EAAoBe,EAAEO,GACtBxC,OAAO8B,eAAeU,EAAI,UAAW,CAAET,YAAY,EAAMK,MAAOA,IACtD,EAAPE,GAA4B,iBAATF,EAAmB,IAAI,IAAIM,KAAON,EAAOlB,EAAoBQ,EAAEc,EAAIE,EAAK,SAASA,GAAO,OAAON,EAAMM,IAAQC,KAAK,KAAMD,IAC9I,OAAOF,GAIRtB,EAAoB0B,EAAI,SAAStB,GAChC,IAAIM,EAASN,GAAUA,EAAOiB,WAC7B,WAAwB,OAAOjB,EAAO,YACtC,WAA8B,OAAOA,GAEtC,OADAJ,EAAoBQ,EAAEE,EAAQ,IAAKA,GAC5BA,GAIRV,EAAoBW,EAAI,SAASgB,EAAQC,GAAY,OAAO9C,OAAOC,UAAUC,eAAeC,KAAK0C,EAAQC,IAGzG5B,EAAoB6B,EAAI,GAExB,IAAIC,EAAaC,OAAO,gBAAkBA,OAAO,iBAAmB,GAChEC,EAAmBF,EAAW3C,KAAKsC,KAAKK,GAC5CA,EAAW3C,KAAOf,EAClB0D,EAAaA,EAAWG,QACxB,IAAI,IAAItD,EAAI,EAAGA,EAAImD,EAAWjD,OAAQF,IAAKP,EAAqB0D,EAAWnD,IAC3E,IAAIU,EAAsB2C,EAI1BzC,EAAgBJ,KAAK,CAAC,EAAE,kBAEjBM,K,6ECvJT,W,mGCAI,EAAS,WAAa,IAAIyC,EAAIC,KAASC,EAAGF,EAAIG,eAAmBC,EAAGJ,EAAIK,MAAMD,IAAIF,EAAG,OAAQF,EAAW,QAAEI,EAAG,MAAM,CAACE,MAAON,EAAgB,aAAEO,MAAM,CAAC,GAAK,cAAc,CAACH,EAAG,MAAM,CAACE,MAAON,EAAa,UAAEO,MAAM,CAAC,MAAQ,MAAM,OAAS,MAAM,QAAU,gBAAgB,CAACH,EAAG,iBAAiB,CAACG,MAAM,CAAC,GAAK,IAAI,GAAK,QAAQ,GAAK,MAAM,GAAK,QAAQ,cAAgB,mBAAmB,CAACH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,IAAI,aAAa,aAAaH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,IAAI,aAAa,aAAaH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,KAAK,aAAa,UAAU,eAAe,SAASH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,MAAM,aAAa,UAAU,eAAe,SAASH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,MAAM,aAAa,UAAU,eAAe,SAASH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,MAAM,aAAa,UAAU,eAAe,QAAQH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,IAAI,aAAa,cAAc,GAAGH,EAAG,iBAAiB,CAACG,MAAM,CAAC,GAAK,IAAI,GAAK,KAAK,GAAK,QAAQ,GAAK,KAAK,GAAK,SAAS,cAAgB,mBAAmB,CAACH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,MAAM,aAAa,aAAaH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,MAAM,aAAa,aAAaH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,KAAK,aAAa,aAAaH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,MAAM,aAAa,aAAaH,EAAG,OAAO,CAACG,MAAM,CAAC,OAAS,IAAI,aAAa,cAAc,GAAGH,EAAG,iBAAiB,CAACG,MAAM,CAAC,GAAK,IAAI,GAAK,MAAM,GAAK,QAAQ,GAAK,MAAM,GAAK,SAAS,aAAa,QAAQH,EAAG,IAAI,CAACI,YAAY,CAAC,UAAY,YAAY,CAACJ,EAAG,OAAO,CAACG,MAAM,CAAC,KAAO,UAAU,EAAI,qBAAqBH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,4TAA4TH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iSAAiSH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2WAA2WH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0MAA0MH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,4eAA4eH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iVAAiVH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,yRAAyRH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,wRAAwRH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,wYAAwYH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2XAA2XH,EAAG,OAAO,CAACI,YAAY,CAAC,iBAAiB,UAAUD,MAAM,CAAC,KAAO,UAAU,EAAI,qBAAqBH,EAAG,OAAO,CAACG,MAAM,CAAC,KAAO,OAAO,OAAS,QAAQ,eAAe,KAAK,EAAI,qBAAqBH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2XAA2XH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,wFAAwFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6EAA6EH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kTAAkTH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0HAA0HH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kHAAkHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gGAAgGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,odAAodH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,wFAAwFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6EAA6EH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kTAAkTH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0HAA0HH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kHAAkHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gGAAgGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,odAAodH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,wFAAwFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6EAA6EH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kTAAkTH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0HAA0HH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kHAAkHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gGAAgGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,+FAA+FH,EAAG,IAAI,CAACA,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,8YAA8YH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,yFAAyFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iFAAiFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0TAA0TH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gIAAgIH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,qHAAqHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,IAAI,CAACA,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,8YAA8YH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,yFAAyFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iFAAiFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0TAA0TH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gIAAgIH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,qHAAqHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,IAAI,CAACA,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,8YAA8YH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,yFAAyFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iFAAiFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0TAA0TH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gIAAgIH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,qHAAqHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,IAAI,CAACA,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,8YAA8YH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,yFAAyFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iFAAiFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0TAA0TH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gIAAgIH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,qHAAqHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,IAAI,CAACA,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2XAA2XH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,wFAAwFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6EAA6EH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kTAAkTH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0HAA0HH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,kHAAkHH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,gGAAgGH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,iGAAiGH,EAAG,OAAO,CAACG,MAAM,CAAC,KAAO,UAAU,EAAI,0BAA0BH,EAAG,OAAO,CAACG,MAAM,CAAC,KAAO,UAAU,EAAI,4BAA4BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,0FAA0FH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,mCAAmCH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,mCAAmCH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,mCAAmCH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,mCAAmCH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6BAA6BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,kBAAkBF,MAAM,CAAC,EAAI,wCAAwCH,EAAG,OAAO,CAACK,YAAY,kBAAkBF,MAAM,CAAC,EAAI,wCAAwCH,EAAG,OAAO,CAACK,YAAY,kBAAkBF,MAAM,CAAC,EAAI,wCAAwCH,EAAG,OAAO,CAACK,YAAY,kBAAkBF,MAAM,CAAC,EAAI,wCAAwCH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6BAA6BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6BAA6BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6BAA6BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,IAAIH,MAAON,EAAI7D,KAAK6D,EAAIU,aAAyB,aAAEH,MAAM,CAAC,EAAI,+BAA+BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2BAA2BH,EAAG,IAAI,CAACA,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,yHAAyHH,EAAG,SAAS,CAACK,YAAY,IAAIF,MAAM,CAAC,GAAK,SAAS,GAAK,QAAQ,EAAI,OAAOH,EAAG,SAAS,CAACK,YAAY,IAAIF,MAAM,CAAC,GAAK,SAAS,GAAK,QAAQ,EAAI,OAAOH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,qFAAqFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,2HAA2HH,EAAG,IAAI,CAACA,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,yHAAyHH,EAAG,SAAS,CAACK,YAAY,IAAIF,MAAM,CAAC,GAAK,SAAS,GAAK,QAAQ,EAAI,OAAOH,EAAG,SAAS,CAACK,YAAY,IAAIF,MAAM,CAAC,GAAK,SAAS,GAAK,QAAQ,EAAI,OAAOH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,mFAAmFH,EAAG,OAAO,CAACK,YAAY,IAAIF,MAAM,CAAC,EAAI,6HAA6HH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,EAAI,KAAK,EAAI,MAAM,MAAQ,KAAK,OAAS,QAAQH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,EAAI,KAAK,EAAI,MAAM,MAAQ,KAAK,OAAS,QAAQH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,EAAI,KAAK,EAAI,MAAM,MAAQ,KAAK,OAAS,QAAQH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,EAAI,KAAK,EAAI,MAAM,MAAQ,KAAK,OAAS,QAAQH,EAAG,OAAO,CAACK,YAAY,wBAAwBF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,SAASH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,SAASH,EAAG,OAAO,CAACK,YAAY,wBAAwBF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,SAASH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,SAASH,EAAG,OAAO,CAACK,YAAY,wBAAwBF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,SAASH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,SAASH,EAAG,OAAO,CAACK,YAAY,wBAAwBF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,SAASH,EAAG,OAAO,CAACK,YAAY,cAAcF,MAAM,CAAC,GAAK,KAAK,GAAK,MAAM,GAAK,KAAK,GAAK,UAAU,KAAKP,EAAIW,MAC5utBC,EAAkB,G,yDCkMtB,GACE,KAAF,mBACE,WAAF,GAIE,KANF,WAOI,MAAJ,CACM,SAAN,EACM,UAAN,iBACM,aAAN,kEACM,cAAN,GACM,WAAN,GAEM,YAAN,EACM,KAAN,CACA,CACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,MAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,YAAR,SAAQ,OAAR,IAAQ,OAAR,EAAQ,OAAR,EACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,EAAQ,OAAR,EACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,gBAGA,CACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,EAAQ,OAAR,EACQ,YAAR,MAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,SAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,OAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,gBAGA,CACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,EAAQ,OAAR,EACQ,YAAR,MAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,SAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,YAAR,OAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,gBAGA,CACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,MAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,YAAR,SAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,YAAR,OAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,gBAGA,CACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,MAAQ,OAAR,IAAQ,OAAR,EAAQ,OAAR,EACQ,YAAR,SAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,OAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,gBAGA,CACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,MAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,SAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,YAAR,OAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,gBAGA,CACQ,YAAR,OAAQ,OAAR,IAAQ,OAAR,EAAQ,OAAR,EACQ,YAAR,MAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,SAAQ,OAAR,IAAQ,OAAR,IAAQ,OAAR,EACQ,YAAR,OAAQ,OAAR,IAAQ,QAAR,IAAQ,OAAR,EACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eACQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,eAAQ,aAAR,mBAcE,QAAF,WACI,OAAJ,4CACI,OAAJ,2CAGE,UAAF,WACI,OAAJ,+CACI,OAAJ,8CAWE,QAAF,CACI,aAAJ,cACM,EAAN,8DACA,mBAAQ,OAAR,oEAGI,SAAJ,YACM,IAAN,QACA,6BACQ,KAAR,6BAII,WAAJ,YACM,GAAN,UAAM,CAEA,IAAN,SAEA,aACQ,KAAR,kBACA,YACU,KAAV,yGACU,KAAV,oCACU,KAAV,8BACU,KAAV,wBAEU,KAAV,uDACU,EAAV,4BACU,WAAV,WACY,KAAZ,8EACA,oBAKI,cAnCJ,SAmCA,SACM,IAAN,YA6CM,SAAN,SACQ,EAAR,YACU,KAAV,CACY,GAAZ,EACY,GAAZ,KAKM,SAAN,OACQ,EAAR,6CACA,mBAAU,OAAV,mEAGM,SAAN,OACA,GACU,EAAV,OACA,yCACY,EAAZ,OACY,OAAZ,uBACc,EAAd,4BACc,EAAd,4BACc,EAAd,4BACc,EAAd,4BACc,EAAd,MACc,EAAd,MACc,EAAd,MACc,EAAd,MACc,EAAd,mCACA,OAGU,EAAV,OAGQ,EAAR,6BACU,QAAV,MACU,SAAV,KAIM,SAAN,WACQ,EAAR,WACU,SAAV,GACU,KAAV,aACU,EAAV,EACU,EAAV,IAEQ,EAAR,WACU,SAAV,GACU,KAAV,aACU,KAAV,CACY,GAAZ,EACY,GAAZ,KAjGM,IAAN,kBACQ,OAAR,WAAU,EAAV,kCACQ,UAAR,WACA,4CACY,EAAZ,4BACY,EAAZ,iBACA,+DAEQ,SAAR,CAAU,OAAV,EAAY,EAAZ,YAAY,EAAZ,cAAU,OAAV,MAGM,IAAN,kBACQ,OAAR,WAAU,EAAV,kCACQ,UAAR,WACA,4CACY,EAAZ,4BACY,EAAZ,iBACA,+DAEQ,SAAR,CAAU,OAAV,EAAY,EAAZ,YAAY,EAAZ,cAAU,OAAV,MAGM,IAAN,kBACQ,OAAR,WAAU,EAAV,kCACQ,UAAR,WACA,4CACY,EAAZ,4BACY,EAAZ,iBACA,+DAEQ,SAAR,CAAU,OAAV,EAAY,EAAZ,YAAY,EAAZ,cAAU,OAAV,MAGM,IAAN,kBACQ,OAAR,WAAU,EAAV,kCACQ,UAAR,WACA,4CACY,EAAZ,4BACY,EAAZ,iBACA,+DAEQ,SAAR,CAAU,OAAV,EAAY,EAAZ,YAAY,EAAZ,cAAU,OAAV,MA4DM,IAAN,mCCzb8T,I,wBCQ1TC,EAAY,eACd,EACA,EACAD,GACA,EACA,KACA,KACA,MAIa,EAAAC,E,QChBfC,OAAIC,OAAOC,eAAgB,EAE3B,IAAIF,OAAI,CACNG,OAAQ,SAAAC,GAAC,OAAIA,EAAEC,MACdC,OAAO,S", "file": "js/app.16c49f3d.js", "sourcesContent": [" \t// install a JSONP callback for chunk loading\n \tfunction webpackJsonpCallback(data) {\n \t\tvar chunkIds = data[0];\n \t\tvar moreModules = data[1];\n \t\tvar executeModules = data[2];\n\n \t\t// add \"moreModules\" to the modules object,\n \t\t// then flag all \"chunkIds\" as loaded and fire callback\n \t\tvar moduleId, chunkId, i = 0, resolves = [];\n \t\tfor(;i < chunkIds.length; i++) {\n \t\t\tchunkId = chunkIds[i];\n \t\t\tif(Object.prototype.hasOwnProperty.call(installedChunks, chunkId) && installedChunks[chunkId]) {\n \t\t\t\tresolves.push(installedChunks[chunkId][0]);\n \t\t\t}\n \t\t\tinstalledChunks[chunkId] = 0;\n \t\t}\n \t\tfor(moduleId in moreModules) {\n \t\t\tif(Object.prototype.hasOwnProperty.call(moreModules, moduleId)) {\n \t\t\t\tmodules[moduleId] = moreModules[moduleId];\n \t\t\t}\n \t\t}\n \t\tif(parentJsonpFunction) parentJsonpFunction(data);\n\n \t\twhile(resolves.length) {\n \t\t\tresolves.shift()();\n \t\t}\n\n \t\t// add entry modules from loaded chunk to deferred list\n \t\tdeferredModules.push.apply(deferredModules, executeModules || []);\n\n \t\t// run deferred modules when all chunks ready\n \t\treturn checkDeferredModules();\n \t};\n \tfunction checkDeferredModules() {\n \t\tvar result;\n \t\tfor(var i = 0; i < deferredModules.length; i++) {\n \t\t\tvar deferredModule = deferredModules[i];\n \t\t\tvar fulfilled = true;\n \t\t\tfor(var j = 1; j < deferredModule.length; j++) {\n \t\t\t\tvar depId = deferredModule[j];\n \t\t\t\tif(installedChunks[depId] !== 0) fulfilled = false;\n \t\t\t}\n \t\t\tif(fulfilled) {\n \t\t\t\tdeferredModules.splice(i--, 1);\n \t\t\t\tresult = __webpack_require__(__webpack_require__.s = deferredModule[0]);\n \t\t\t}\n \t\t}\n\n \t\treturn result;\n \t}\n\n \t// The module cache\n \tvar installedModules = {};\n\n \t// object to store loaded and loading chunks\n \t// undefined = chunk not loaded, null = chunk preloaded/prefetched\n \t// Promise = chunk loading, 0 = chunk loaded\n \tvar installedChunks = {\n \t\t\"app\": 0\n \t};\n\n \tvar deferredModules = [];\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n \tvar jsonpArray = window[\"webpackJsonp\"] = window[\"webpackJsonp\"] || [];\n \tvar oldJsonpFunction = jsonpArray.push.bind(jsonpArray);\n \tjsonpArray.push = webpackJsonpCallback;\n \tjsonpArray = jsonpArray.slice();\n \tfor(var i = 0; i < jsonpArray.length; i++) webpackJsonpCallback(jsonpArray[i]);\n \tvar parentJsonpFunction = oldJsonpFunction;\n\n\n \t// add entry module to deferred list\n \tdeferredModules.push([0,\"chunk-vendors\"]);\n \t// run deferred modules when ready\n \treturn checkDeferredModules();\n", "export * from \"-!../node_modules/mini-css-extract-plugin/dist/loader.js??ref--6-oneOf-1-0!../node_modules/css-loader/dist/cjs.js??ref--6-oneOf-1-1!../node_modules/vue-loader/lib/loaders/stylePostLoader.js!../node_modules/postcss-loader/src/index.js??ref--6-oneOf-1-2!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=style&index=0&lang=css&\"", "var render = function () {var _vm=this;var _h=_vm.$createElement;var _c=_vm._self._c||_h;return (_vm.NuiOpen)?_c('div',{style:(_vm.position_nui),attrs:{\"id\":\"container\"}},[_c('svg',{style:(_vm.size_game),attrs:{\"width\":\"907\",\"height\":\"907\",\"viewBox\":\"0 0 907 907\"}},[_c('linearGradient',{attrs:{\"id\":\"a\",\"y1\":\"453.5\",\"x2\":\"907\",\"y2\":\"453.5\",\"gradientUnits\":\"userSpaceOnUse\"}},[_c('stop',{attrs:{\"offset\":\"0\",\"stop-color\":\"#1d1d1b\"}}),_c('stop',{attrs:{\"offset\":\"0\",\"stop-color\":\"#272726\"}}),_c('stop',{attrs:{\"offset\":\".2\",\"stop-color\":\"#262625\",\"stop-opacity\":\".93\"}}),_c('stop',{attrs:{\"offset\":\".35\",\"stop-color\":\"#232322\",\"stop-opacity\":\".69\"}}),_c('stop',{attrs:{\"offset\":\".48\",\"stop-color\":\"#1e1e1c\",\"stop-opacity\":\".29\"}}),_c('stop',{attrs:{\"offset\":\".51\",\"stop-color\":\"#1d1d1b\",\"stop-opacity\":\".2\"}}),_c('stop',{attrs:{\"offset\":\"1\",\"stop-color\":\"#1d1d1b\"}})],1),_c('linearGradient',{attrs:{\"id\":\"b\",\"x1\":\"35\",\"y1\":\"-2.43\",\"x2\":\"35\",\"y2\":\"890.57\",\"gradientUnits\":\"userSpaceOnUse\"}},[_c('stop',{attrs:{\"offset\":\".77\",\"stop-color\":\"#393e42\"}}),_c('stop',{attrs:{\"offset\":\".83\",\"stop-color\":\"#35393d\"}}),_c('stop',{attrs:{\"offset\":\".9\",\"stop-color\":\"#292c2e\"}}),_c('stop',{attrs:{\"offset\":\".98\",\"stop-color\":\"#151616\"}}),_c('stop',{attrs:{\"offset\":\"1\",\"stop-color\":\"#0f0f0f\"}})],1),_c('linearGradient',{attrs:{\"id\":\"c\",\"x1\":\"874\",\"y1\":\"-2.43\",\"x2\":\"874\",\"y2\":\"890.57\",\"xlink:href\":\"#b\"}}),_c('g',{staticStyle:{\"isolation\":\"isolate\"}},[_c('path',{attrs:{\"fill\":\"#1e2021\",\"d\":\"M0 0h907v907H0z\"}}),_c('path',{staticClass:\"c\",attrs:{\"d\":\"M838 615.45c-.54-.06-1.51-.13-2.87-.24-75.79-6-130.78-23.35-163.56-36.9-41.49-17.15-75-39.34-99.63-65.94-30.57-33-47.53-73-50.43-118.77-5.21-82.25 15.57-282 46.35-389.22L588 5c-30.3 105.55-52.63 306.59-47.52 387.4 4.79 75.74 51.34 132.38 138.36 168.35 31.51 13 84.47 29.77 157.79 35.52 1.59.12 2.73.22 3.36.28z\"}}),_c('path',{staticClass:\"d\",attrs:{\"d\":\"M723.54 409.4q-9.16 0-18.14-.41c-54.53-2.51-106.1-16.15-162.27-42.91-45.22-21.55-77.07-89.15-94.69-200.95C435.59 83.58 435.28 7 435.28 6.2h19c0 .75.32 76.1 13 156.23C483.6 266 512.66 330.52 551.3 348.92c110.05 52.44 210.92 54.93 348.16 8.58l6.08 18c-67.37 22.75-127.14 33.9-182 33.9z\"}}),_c('path',{staticClass:\"e\",attrs:{\"d\":\"M802.12 286.19c-48.89 0-116.11-4.85-174.4-26.27-28.81-10.59-52.24-31.05-69.63-60.8-13.58-23.21-23.48-52.12-29.45-85.9-10-56.88-5.56-107.58-5.36-109.71l18.92 1.74c-.06.68-4.44 50.72 5.22 105 12.6 70.9 41.82 115.24 86.86 131.79 113.27 41.67 265.24 18.83 266.72 18.6l3 18.77c-1.61.25-39.91 6.11-93.22 6.73-2.84.03-5.72.05-8.66.05zM542.2 5.25l-9.46-.87 9.46.87z\"}}),_c('path',{staticClass:\"d\",attrs:{\"d\":\"M564.31 908.05c-.12-2.15-12.69-218-22.63-436.16C521.47 28.25 531.45 7.87 535.21.2l17.06 8.36c-1.78 4.08-5.66 27.56-2.84 156.19 1.68 76.69 5.48 180.3 11.31 308 9.91 217.4 22.42 432.05 22.54 434.25z\"}}),_c('path',{staticClass:\"f\",attrs:{\"d\":\"M437.33 908l-19-.23c0-2.19 2.74-221.32 8.25-440.78C437.82 19.6 449.22 6.94 454.7.85l14.12 12.71a4.64 4.64 0 00.61-.82c-2.06 4-7.52 27.05-13.71 155.12-3.69 76.35-7.16 179.62-10.33 306.93-5.39 216.84-8.03 431.07-8.06 433.21zM809.3 715.45c-18.52 0-38.52-.48-58.78-1.76-106.86-6.78-169.57-32.1-186.37-75.24C526.86 542.7 546.94 27.68 547.82 5.8l19 .76c-.21 5.31-20.81 532.91 15.05 625 13.68 35.12 72.35 57 169.67 63.16 75.88 4.84 148.36-1.94 149.08-2l1.8 18.92c-.56.03-40.92 3.81-93.12 3.81z\"}}),_c('path',{staticClass:\"e\",attrs:{\"d\":\"M60.47 726.62c-31.85 0-51.9-.78-52.35-.8l.76-19c1 0 103.72 4 215.44-6.35 145.77-13.52 240.53-44.87 274-90.65 33.94-46.39 51-164.9 49.35-342.73-1.23-135.54-13.13-258.44-13.25-259.66l18.91-1.86c.12 1.24 12.09 124.85 13.37 261.23 1.73 184.63-16.11 303.82-53 354.26-37.35 51-134.18 84.14-287.8 98.37-63.64 5.89-124.35 7.19-165.43 7.19z\"}}),_c('path',{staticClass:\"c\",attrs:{\"d\":\"M132.2 545.37a1197 1197 0 01-125.82-6.61l2.24-18.86c2.75.32 275.82 31.71 364.55-47.49 36.15-32.26 60.91-122.55 71.6-261.1 8.19-106.14 5-203.52 5-204.49l19-.64c0 1 3.23 99.26-5 206.42-11.22 145.7-37.43 237.88-77.89 274-40.3 36-116.66 55.62-227 58.45-9.05.22-17.98.32-26.68.32z\"}}),_c('path',{staticClass:\"e\",attrs:{\"d\":\"M286.57 299.68a345.5 345.5 0 01-37.6-2.24C146.67 286.3 8.79 188.39 3 184.23l11-15.46c1.38 1 139.66 99.19 237 109.79 63 6.86 103.74-6.26 140.25-32C463 196 449.1 125 455 6l19 1c-3.27 65.51 3 118-11.6 163.48-12.69 39.51-34 69.45-58.8 90.57-36.42 31.04-79.72 38.63-117.03 38.63z\"}}),_c('path',{staticClass:\"d\",attrs:{\"d\":\"M251.21 228.7c-31.08 0-62.8-5.84-94.43-17.41-27.89-10.2-55.77-24.86-82.86-43.55C27.89 136 .3 103.85-.85 102.5l14.46-12.33c.27.32 27.42 31.87 71.53 62.22 40.25 27.7 103.36 60.09 175.47 57.12 78-3.23 121.3-15.37 149.4-41.89 30.54-28.82 45.72-78.74 49.25-161.85l19 .81c-1.87 44.19-7 79.2-15.55 107-8.91 28.88-21.87 51.07-39.64 67.84-31.77 30-78.6 43.62-161.66 47.05-3.41.16-6.8.23-10.2.23z\"}}),_c('path',{staticClass:\"f\",attrs:{\"d\":\"M226.25 455.3q-42.93 0-100.76-12a967.1 967.1 0 01-122.4-34.5L9.67 391a948.51 948.51 0 00119.89 33.76c82.92 17.18 142.67 15.09 172.82-6 7.09-5 14.24-9.69 21.15-14.26 35.29-23.31 68.62-45.33 94.2-99.51 29.13-61.71 32.7-160.09 35.61-300.77l19 .4c-3 145.39-6.71 243.41-37.43 308.48-27.85 59-65 83.53-100.91 107.25-6.8 4.5-13.84 9.15-20.72 14-19.98 13.94-49.1 20.95-87.03 20.95z\"}}),_c('path',{staticStyle:{\"mix-blend-mode\":\"darken\"},attrs:{\"fill\":\"url(#a)\",\"d\":\"M0 0h907v907H0z\"}}),_c('path',{attrs:{\"fill\":\"none\",\"stroke\":\"black\",\"stroke-width\":\"14\",\"d\":\"M0 0h907v907H0z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M106 381.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 370.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 367.15L85 364c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 396c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 390 89 391s-2 5-4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M108.89 393.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M110 387.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M103.05 383.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 367.85L97 364c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 384.18l-6.71-4.36C71.51 372.09 79 360 83 360a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M76 390.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 368.19 83.65 380.59 76 390.45z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M87.14 388.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M96.05 388.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M102.05 388.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18zM106 564.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 553.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 550.15L85 547c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 579c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 573 89 574s-2 5-4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M108.89 576.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M110 570.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M103.05 566.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 550.85L97 547c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 567.18l-6.71-4.36C71.51 555.09 79 543 83 543a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M76 573.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 551.19 83.65 563.59 76 573.45z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M87.14 571.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M96.05 571.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M102.05 571.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18zM106 752.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 741.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 738.15L85 735c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 767c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 761 89 762s-2 5-4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M108.89 764.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M110 758.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M103.05 754.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 738.85L97 735c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 755.18l-6.71-4.36C71.51 743.09 79 731 83 731a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M76 761.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 739.19 83.65 751.59 76 761.45z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M87.14 759.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M96.05 759.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M102.05 759.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18z\"}}),_c('g',[_c('path',{staticClass:\"h\",attrs:{\"d\":\"M763.69 751.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 740.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 737.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 766c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M760.84 763.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M759.76 757.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M766.68 753.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 737.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 754.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M793.73 760.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M782.59 758.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M773.68 758.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M767.68 758.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"}})]),_c('g',[_c('path',{staticClass:\"h\",attrs:{\"d\":\"M763.69 566.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 555.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 552.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 581c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M760.84 578.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M759.76 572.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M766.68 568.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 552.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 569.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M793.73 575.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M782.59 573.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M773.68 573.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M767.68 573.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"}})]),_c('g',[_c('path',{staticClass:\"h\",attrs:{\"d\":\"M763.69 382.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 371.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 368.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 397c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M760.84 394.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M759.76 388.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M766.68 384.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 368.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 385.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M793.73 391.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M782.59 389.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M773.68 389.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M767.68 389.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"}})]),_c('g',[_c('path',{staticClass:\"h\",attrs:{\"d\":\"M763.69 193.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 182.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 179.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 208c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M760.84 205.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M759.76 199.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M766.68 195.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 179.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 196.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M793.73 202.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M782.59 200.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M773.68 200.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M767.68 200.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"}})]),_c('g',[_c('path',{staticClass:\"h\",attrs:{\"d\":\"M106 191.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 180.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 177.15L85 174c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 206c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 200 89 201s-2 5-4 5z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M108.89 203.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M110 197.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M103.05 193.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 177.85L97 174c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 194.18l-6.71-4.36C71.51 182.09 79 170 83 170a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M76 200.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 178.19 83.65 190.59 76 200.45z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M87.14 198.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M96.05 198.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"}}),_c('path',{staticClass:\"h\",attrs:{\"d\":\"M102.05 198.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18z\"}})]),_c('path',{attrs:{\"fill\":\"url(#b)\",\"d\":\"M7.5 6.5h55v893h-55z\"}}),_c('path',{attrs:{\"fill\":\"url(#c)\",\"d\":\"M846.5 6.5h55v893h-55z\"}}),_c('path',{staticClass:\"k\",attrs:{\"d\":\"M7.5 140.5h56v63h-56zM7.5 330.5h56v63h-56zM7.5 515.5h56v63h-56zM7.5 701.5h56v63h-56z\"}}),_c('path',{staticClass:\"l\",attrs:{\"d\":\"M8.51 143.16H59.5v24.34H8.51z\"}}),_c('path',{staticClass:\"m\",attrs:{\"d\":\"M8.5 167.5h66v33h-66z\"}}),_c('path',{staticClass:\"n\",attrs:{\"d\":\"M8.5 176.5h66v17h-66z\"}}),_c('path',{staticClass:\"l\",attrs:{\"d\":\"M8.51 333.16H59.5v24.34H8.51z\"}}),_c('path',{staticClass:\"o\",attrs:{\"d\":\"M8.5 357.5h66v33h-66z\"}}),_c('path',{staticClass:\"p\",attrs:{\"d\":\"M8.5 366.5h66v17h-66z\"}}),_c('path',{staticClass:\"l\",attrs:{\"d\":\"M8.51 518.16H59.5v24.34H8.51z\"}}),_c('path',{staticClass:\"q\",attrs:{\"d\":\"M8.5 542.5h66v33h-66z\"}}),_c('path',{staticClass:\"r\",attrs:{\"d\":\"M8.5 551.5h66v17h-66z\"}}),_c('path',{staticClass:\"l\",attrs:{\"d\":\"M8.51 704.16H59.5v24.34H8.51z\"}}),_c('path',{staticClass:\"s\",attrs:{\"d\":\"M8.5 728.5h66v33h-66z\"}}),_c('path',{staticClass:\"t\",attrs:{\"d\":\"M8.5 737.5h66v17h-66z\"}}),_c('path',{staticClass:\"k\",attrs:{\"d\":\"M845.5 142.5h56v63h-56z\"}}),_c('path',{staticClass:\"o\",style:(_vm.data[_vm.game_change].color1_fio_1),attrs:{\"d\":\"M794.5 169.5h108v33h-108z\"}}),_c('path',{staticClass:\"p\",style:(_vm.data[_vm.game_change].color2_fio_1),attrs:{\"d\":\"M794.5 178.5h108v17h-108z\"}}),_c('path',{staticClass:\"l light light-1\",attrs:{\"d\":\"M850.51 145.16h50.99v24.34h-50.99z\"}}),_c('path',{staticClass:\"l light light-2\",attrs:{\"d\":\"M850.51 333.16h50.99v24.34h-50.99z\"}}),_c('path',{staticClass:\"l light light-3\",attrs:{\"d\":\"M850.51 517.16h50.99v24.34h-50.99z\"}}),_c('path',{staticClass:\"l light light-4\",attrs:{\"d\":\"M850.51 702.16h50.99v24.34h-50.99z\"}}),_c('path',{staticClass:\"k\",attrs:{\"d\":\"M845.5 514.5h56v63h-56z\"}}),_c('path',{staticClass:\"k\",attrs:{\"d\":\"M845.5 699.5h56v63h-56z\"}}),_c('path',{staticClass:\"k\",attrs:{\"d\":\"M845.5 330.5h56v63h-56z\"}}),_c('path',{staticClass:\"s\",style:(_vm.data[_vm.game_change].color1_fio_4),attrs:{\"d\":\"M793.5 726.5h108v33h-108z\"}}),_c('path',{staticClass:\"t\",style:(_vm.data[_vm.game_change].color2_fio_4),attrs:{\"d\":\"M793.5 735.5h108v17h-108z\"}}),_c('path',{staticClass:\"q\",style:(_vm.data[_vm.game_change].color1_fio_3),attrs:{\"d\":\"M791.5 541.5h110v33h-110z\"}}),_c('path',{staticClass:\"r\",style:(_vm.data[_vm.game_change].color2_fio_3),attrs:{\"d\":\"M791.5 550.5h110v17h-110z\"}}),_c('path',{staticClass:\"m\",style:(_vm.data[_vm.game_change].color1_fio_2),attrs:{\"d\":\"M791.5 357.5h111v33h-111z\"}}),_c('path',{staticClass:\"n\",style:(_vm.data[_vm.game_change].color2_fio_2),attrs:{\"d\":\"M791.5 366.5h111v17h-111z\"}}),_c('path',{staticClass:\"m\",attrs:{\"d\":\"M8.5 167.5h66v33h-66z\"}}),_c('path',{staticClass:\"n\",attrs:{\"d\":\"M8.5 176.5h66v17h-66z\"}}),_c('g',[_c('path',{staticClass:\"v\",attrs:{\"d\":\"M427.82 87.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V86.11l-12-.39s-4-14-22-14-19 14-19 14z\"}}),_c('circle',{staticClass:\"w\",attrs:{\"cx\":\"431.78\",\"cy\":\"97.72\",\"r\":\"2\"}}),_c('circle',{staticClass:\"w\",attrs:{\"cx\":\"484.78\",\"cy\":\"97.72\",\"r\":\"2\"}}),_c('path',{staticClass:\"x\",attrs:{\"d\":\"M439.94 100.71l6-27-4 4-8 30 6-7zM476.94 107.71l-.12-21.99-5.88-10.01-2 27 8 5z\"}}),_c('path',{staticClass:\"y\",attrs:{\"d\":\"M427.82 87.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V86.11l-12-.39s-4-14-22-14-19 14-19 14z\"}})]),_c('g',[_c('path',{staticClass:\"v\",attrs:{\"d\":\"M521.82 77.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V76.11l-12-.39s-4-14-22-14-19 14-19 14z\"}}),_c('circle',{staticClass:\"w\",attrs:{\"cx\":\"525.78\",\"cy\":\"87.72\",\"r\":\"2\"}}),_c('circle',{staticClass:\"w\",attrs:{\"cx\":\"578.78\",\"cy\":\"87.72\",\"r\":\"2\"}}),_c('path',{staticClass:\"x\",attrs:{\"d\":\"M533.94 90.71l6-27-4 4-8 30 6-7zM570.94 97.71l-.12-21.99-5.88-10.01-2 27 8 5z\"}}),_c('path',{staticClass:\"y\",attrs:{\"d\":\"M521.82 77.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V76.11l-12-.39s-4-14-22-14-19 14-19 14z\"}})])]),_c('rect',{staticClass:\"drag drag-1\",attrs:{\"x\":\"60\",\"y\":\"165\",\"width\":\"60\",\"height\":\"40\"}}),_c('rect',{staticClass:\"drag drag-2\",attrs:{\"x\":\"60\",\"y\":\"355\",\"width\":\"60\",\"height\":\"40\"}}),_c('rect',{staticClass:\"drag drag-3\",attrs:{\"x\":\"60\",\"y\":\"540\",\"width\":\"60\",\"height\":\"40\"}}),_c('rect',{staticClass:\"drag drag-4\",attrs:{\"x\":\"60\",\"y\":\"725\",\"width\":\"60\",\"height\":\"40\"}}),_c('line',{staticClass:\"line line-back line-1\",attrs:{\"x1\":\"70\",\"y1\":\"185\",\"x2\":\"70\",\"y2\":\"185\"}}),_c('line',{staticClass:\"line line-1\",attrs:{\"x1\":\"70\",\"y1\":\"185\",\"x2\":\"70\",\"y2\":\"185\"}}),_c('line',{staticClass:\"line line-back line-2\",attrs:{\"x1\":\"65\",\"y1\":\"375\",\"x2\":\"65\",\"y2\":\"375\"}}),_c('line',{staticClass:\"line line-2\",attrs:{\"x1\":\"65\",\"y1\":\"375\",\"x2\":\"65\",\"y2\":\"375\"}}),_c('line',{staticClass:\"line line-back line-3\",attrs:{\"x1\":\"65\",\"y1\":\"560\",\"x2\":\"65\",\"y2\":\"560\"}}),_c('line',{staticClass:\"line line-3\",attrs:{\"x1\":\"65\",\"y1\":\"560\",\"x2\":\"65\",\"y2\":\"560\"}}),_c('line',{staticClass:\"line line-back line-4\",attrs:{\"x1\":\"65\",\"y1\":\"745\",\"x2\":\"65\",\"y2\":\"745\"}}),_c('line',{staticClass:\"line line-4\",attrs:{\"x1\":\"65\",\"y1\":\"745\",\"x2\":\"65\",\"y2\":\"745\"}})],1)]):_vm._e()}\nvar staticRenderFns = []\n\nexport { render, staticRenderFns }", "<template>\r\n    <div id=\"container\" v-if=\"NuiOpen\" :style=\"position_nui\">\r\n        <svg :style=\"size_game\" width=\"907\" height=\"907\" viewBox=\"0 0 907 907\">\r\n        <linearGradient id=\"a\" y1=\"453.5\" x2=\"907\" y2=\"453.5\" gradientUnits=\"userSpaceOnUse\">\r\n            <stop offset=\"0\" stop-color=\"#1d1d1b\"/>\r\n            <stop offset=\"0\" stop-color=\"#272726\"/>\r\n            <stop offset=\".2\" stop-color=\"#262625\" stop-opacity=\".93\"/>\r\n            <stop offset=\".35\" stop-color=\"#232322\" stop-opacity=\".69\"/>\r\n            <stop offset=\".48\" stop-color=\"#1e1e1c\" stop-opacity=\".29\"/>\r\n            <stop offset=\".51\" stop-color=\"#1d1d1b\" stop-opacity=\".2\"/>\r\n            <stop offset=\"1\" stop-color=\"#1d1d1b\"/>\r\n        </linearGradient>\r\n        <linearGradient id=\"b\" x1=\"35\" y1=\"-2.43\" x2=\"35\" y2=\"890.57\" gradientUnits=\"userSpaceOnUse\">\r\n            <stop offset=\".77\" stop-color=\"#393e42\"/>\r\n            <stop offset=\".83\" stop-color=\"#35393d\"/>\r\n            <stop offset=\".9\" stop-color=\"#292c2e\"/>\r\n            <stop offset=\".98\" stop-color=\"#151616\"/>\r\n            <stop offset=\"1\" stop-color=\"#0f0f0f\"/>\r\n        </linearGradient>\r\n        <linearGradient id=\"c\" x1=\"874\" y1=\"-2.43\" x2=\"874\" y2=\"890.57\" xlink:href=\"#b\"/>\r\n        <g style=\"isolation:isolate\">\r\n            <path fill=\"#1e2021\" d=\"M0 0h907v907H0z\"/>\r\n            <path class=\"c\" d=\"M838 615.45c-.54-.06-1.51-.13-2.87-.24-75.79-6-130.78-23.35-163.56-36.9-41.49-17.15-75-39.34-99.63-65.94-30.57-33-47.53-73-50.43-118.77-5.21-82.25 15.57-282 46.35-389.22L588 5c-30.3 105.55-52.63 306.59-47.52 387.4 4.79 75.74 51.34 132.38 138.36 168.35 31.51 13 84.47 29.77 157.79 35.52 1.59.12 2.73.22 3.36.28z\"/>\r\n            <path class=\"d\" d=\"M723.54 409.4q-9.16 0-18.14-.41c-54.53-2.51-106.1-16.15-162.27-42.91-45.22-21.55-77.07-89.15-94.69-200.95C435.59 83.58 435.28 7 435.28 6.2h19c0 .75.32 76.1 13 156.23C483.6 266 512.66 330.52 551.3 348.92c110.05 52.44 210.92 54.93 348.16 8.58l6.08 18c-67.37 22.75-127.14 33.9-182 33.9z\"/>\r\n            <path class=\"e\" d=\"M802.12 286.19c-48.89 0-116.11-4.85-174.4-26.27-28.81-10.59-52.24-31.05-69.63-60.8-13.58-23.21-23.48-52.12-29.45-85.9-10-56.88-5.56-107.58-5.36-109.71l18.92 1.74c-.06.68-4.44 50.72 5.22 105 12.6 70.9 41.82 115.24 86.86 131.79 113.27 41.67 265.24 18.83 266.72 18.6l3 18.77c-1.61.25-39.91 6.11-93.22 6.73-2.84.03-5.72.05-8.66.05zM542.2 5.25l-9.46-.87 9.46.87z\"/>\r\n            <path class=\"d\" d=\"M564.31 908.05c-.12-2.15-12.69-218-22.63-436.16C521.47 28.25 531.45 7.87 535.21.2l17.06 8.36c-1.78 4.08-5.66 27.56-2.84 156.19 1.68 76.69 5.48 180.3 11.31 308 9.91 217.4 22.42 432.05 22.54 434.25z\"/>\r\n            <path class=\"f\" d=\"M437.33 908l-19-.23c0-2.19 2.74-221.32 8.25-440.78C437.82 19.6 449.22 6.94 454.7.85l14.12 12.71a4.64 4.64 0 00.61-.82c-2.06 4-7.52 27.05-13.71 155.12-3.69 76.35-7.16 179.62-10.33 306.93-5.39 216.84-8.03 431.07-8.06 433.21zM809.3 715.45c-18.52 0-38.52-.48-58.78-1.76-106.86-6.78-169.57-32.1-186.37-75.24C526.86 542.7 546.94 27.68 547.82 5.8l19 .76c-.21 5.31-20.81 532.91 15.05 625 13.68 35.12 72.35 57 169.67 63.16 75.88 4.84 148.36-1.94 149.08-2l1.8 18.92c-.56.03-40.92 3.81-93.12 3.81z\"/>\r\n            <path class=\"e\" d=\"M60.47 726.62c-31.85 0-51.9-.78-52.35-.8l.76-19c1 0 103.72 4 215.44-6.35 145.77-13.52 240.53-44.87 274-90.65 33.94-46.39 51-164.9 49.35-342.73-1.23-135.54-13.13-258.44-13.25-259.66l18.91-1.86c.12 1.24 12.09 124.85 13.37 261.23 1.73 184.63-16.11 303.82-53 354.26-37.35 51-134.18 84.14-287.8 98.37-63.64 5.89-124.35 7.19-165.43 7.19z\"/>\r\n            <path class=\"c\" d=\"M132.2 545.37a1197 1197 0 01-125.82-6.61l2.24-18.86c2.75.32 275.82 31.71 364.55-47.49 36.15-32.26 60.91-122.55 71.6-261.1 8.19-106.14 5-203.52 5-204.49l19-.64c0 1 3.23 99.26-5 206.42-11.22 145.7-37.43 237.88-77.89 274-40.3 36-116.66 55.62-227 58.45-9.05.22-17.98.32-26.68.32z\"/>\r\n            <path class=\"e\" d=\"M286.57 299.68a345.5 345.5 0 01-37.6-2.24C146.67 286.3 8.79 188.39 3 184.23l11-15.46c1.38 1 139.66 99.19 237 109.79 63 6.86 103.74-6.26 140.25-32C463 196 449.1 125 455 6l19 1c-3.27 65.51 3 118-11.6 163.48-12.69 39.51-34 69.45-58.8 90.57-36.42 31.04-79.72 38.63-117.03 38.63z\"/>\r\n            <path class=\"d\" d=\"M251.21 228.7c-31.08 0-62.8-5.84-94.43-17.41-27.89-10.2-55.77-24.86-82.86-43.55C27.89 136 .3 103.85-.85 102.5l14.46-12.33c.27.32 27.42 31.87 71.53 62.22 40.25 27.7 103.36 60.09 175.47 57.12 78-3.23 121.3-15.37 149.4-41.89 30.54-28.82 45.72-78.74 49.25-161.85l19 .81c-1.87 44.19-7 79.2-15.55 107-8.91 28.88-21.87 51.07-39.64 67.84-31.77 30-78.6 43.62-161.66 47.05-3.41.16-6.8.23-10.2.23z\"/>\r\n            <path class=\"f\" d=\"M226.25 455.3q-42.93 0-100.76-12a967.1 967.1 0 01-122.4-34.5L9.67 391a948.51 948.51 0 00119.89 33.76c82.92 17.18 142.67 15.09 172.82-6 7.09-5 14.24-9.69 21.15-14.26 35.29-23.31 68.62-45.33 94.2-99.51 29.13-61.71 32.7-160.09 35.61-300.77l19 .4c-3 145.39-6.71 243.41-37.43 308.48-27.85 59-65 83.53-100.91 107.25-6.8 4.5-13.84 9.15-20.72 14-19.98 13.94-49.1 20.95-87.03 20.95z\"/>\r\n            <path style=\"mix-blend-mode:darken\" fill=\"url(#a)\" d=\"M0 0h907v907H0z\"/>\r\n            <path fill=\"none\" stroke=\"black\" stroke-width=\"14\" d=\"M0 0h907v907H0z\"/>\r\n            \r\n            <path class=\"h\" d=\"M106 381.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 370.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 367.15L85 364c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 396c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 390 89 391s-2 5-4 5z\"/>\r\n            <path class=\"h\" d=\"M108.89 393.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"/>\r\n            <path class=\"h\" d=\"M110 387.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"/>\r\n            <path class=\"h\" d=\"M103.05 383.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 367.85L97 364c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 384.18l-6.71-4.36C71.51 372.09 79 360 83 360a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M76 390.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 368.19 83.65 380.59 76 390.45z\"/>\r\n            <path class=\"h\" d=\"M87.14 388.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M96.05 388.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"/>\r\n            <path class=\"h\" d=\"M102.05 388.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18zM106 564.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 553.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 550.15L85 547c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 579c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 573 89 574s-2 5-4 5z\"/>\r\n            <path class=\"h\" d=\"M108.89 576.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"/>\r\n            <path class=\"h\" d=\"M110 570.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"/>\r\n            <path class=\"h\" d=\"M103.05 566.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 550.85L97 547c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 567.18l-6.71-4.36C71.51 555.09 79 543 83 543a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M76 573.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 551.19 83.65 563.59 76 573.45z\"/>\r\n            <path class=\"h\" d=\"M87.14 571.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M96.05 571.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"/>\r\n            <path class=\"h\" d=\"M102.05 571.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18zM106 752.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 741.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 738.15L85 735c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 767c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 761 89 762s-2 5-4 5z\"/>\r\n            <path class=\"h\" d=\"M108.89 764.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"/>\r\n            <path class=\"h\" d=\"M110 758.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"/>\r\n            <path class=\"h\" d=\"M103.05 754.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 738.85L97 735c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 755.18l-6.71-4.36C71.51 743.09 79 731 83 731a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M76 761.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 739.19 83.65 751.59 76 761.45z\"/>\r\n            <path class=\"h\" d=\"M87.14 759.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M96.05 759.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"/>\r\n            <path class=\"h\" d=\"M102.05 759.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18z\"/>\r\n            <g>\r\n            <path class=\"h\" d=\"M763.69 751.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 740.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 737.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 766c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"/>\r\n            <path class=\"h\" d=\"M760.84 763.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"/>\r\n            <path class=\"h\" d=\"M759.76 757.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"/>\r\n            <path class=\"h\" d=\"M766.68 753.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 737.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 754.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M793.73 760.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"/>\r\n            <path class=\"h\" d=\"M782.59 758.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M773.68 758.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"/>\r\n            <path class=\"h\" d=\"M767.68 758.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"/>\r\n            </g>\r\n            <g>\r\n            <path class=\"h\" d=\"M763.69 566.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 555.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 552.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 581c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"/>\r\n            <path class=\"h\" d=\"M760.84 578.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"/>\r\n            <path class=\"h\" d=\"M759.76 572.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"/>\r\n            <path class=\"h\" d=\"M766.68 568.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 552.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 569.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M793.73 575.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"/>\r\n            <path class=\"h\" d=\"M782.59 573.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M773.68 573.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"/>\r\n            <path class=\"h\" d=\"M767.68 573.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"/>\r\n            </g>\r\n            <g>\r\n            <path class=\"h\" d=\"M763.69 382.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 371.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 368.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 397c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"/>\r\n            <path class=\"h\" d=\"M760.84 394.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"/>\r\n            <path class=\"h\" d=\"M759.76 388.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"/>\r\n            <path class=\"h\" d=\"M766.68 384.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 368.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 385.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M793.73 391.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"/>\r\n            <path class=\"h\" d=\"M782.59 389.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M773.68 389.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"/>\r\n            <path class=\"h\" d=\"M767.68 389.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"/>\r\n            </g>\r\n            <g>\r\n            <path class=\"h\" d=\"M763.69 193.86a20.48 20.48 0 004.77-.49l-2-7.75a17.64 17.64 0 01-10.15-1.29l-3.2 7.34a26.33 26.33 0 0010.58 2.19zM795.4 182.94l7-3.88c-1.76-3.16-6.4-10.66-11.53-11.94l-1.94 7.76c1.24.43 4.4 4.36 6.47 8.06zM776.42 179.15l8.31-3.15c-4.78-3.74-12.29-12.63-14.64-12.31s-5.94 5.36-5.94 5.36c1.33 1.48 9.02 7.56 12.27 10.1zM784.73 208c2 0 8.59-8.63 8.86-9.28l-7.37-3.1s-5.49 6.38-5.49 7.38 2 5 4 5z\"/>\r\n            <path class=\"h\" d=\"M760.84 205.66c.32-1.94 4.79-8.62 8.89-9.66l-1.59-3.68c-2.3.57-13.83 3.85-15.19 12z\"/>\r\n            <path class=\"h\" d=\"M759.76 199.89a35.14 35.14 0 019.09-1.9l-.53-8a42.81 42.81 0 00-11.63 2.49z\"/>\r\n            <path class=\"h\" d=\"M766.68 195.85l1.58-7.84c-1.16-.24-18.53-2-19.53 1s1.19 5.44 3.59 6.72 11.79-.4 14.36.12zM764.54 179.85l8.19-3.85c-.06 0-14.27-8.13-18.13-8.56s-4.4 4.69-3.64 7.12 13.28 5.2 13.58 5.29zM796.54 196.18l6.71-4.36c-5-7.73-12.52-19.82-16.52-19.82a5.16 5.16 0 00-5.05 3.33l-.09-.06c1.77 1.43 9.14 11.97 14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M793.73 202.45l6.32-4.9c-7.26-9.37-15.64-20.76-16.51-22.83a1.89 1.89 0 00.06.18l-7.7 2.2c.89 3.09 10.19 15.49 17.83 25.35z\"/>\r\n            <path class=\"h\" d=\"M782.59 200.26l6.61-4.52c-5.62-8.21-11.91-18.59-12.32-21a1.77 1.77 0 010 .22h-8c.01 4.37 7.46 16.16 13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M773.68 200.18l6.7-4.36c-5.5-8.44-9.39-20.88-9.43-21l-7.64 2.36c.17.56 4.27 13.64 10.37 23z\"/>\r\n            <path class=\"h\" d=\"M767.68 200.18l6.7-4.36c-5.5-8.44-9.61-19.7-9.65-19.82l-7 2c.17.55 3.85 12.82 9.95 22.18z\"/>\r\n            </g>\r\n            <g>\r\n            <path class=\"h\" d=\"M106 191.86a20.48 20.48 0 01-4.77-.49l2-7.75a17.64 17.64 0 0010.15-1.29l3.2 7.34a26.29 26.29 0 01-10.58 2.19zM74.33 180.94l-7-3.88c1.76-3.16 6.4-10.66 11.53-11.94l1.94 7.76c-1.24.43-4.4 4.36-6.47 8.06zM93.31 177.15L85 174c4.78-3.74 12.29-12.63 14.65-12.31s5.93 5.36 5.93 5.36c-1.33 1.48-9.02 7.56-12.27 10.1zM85 206c-2 0-8.59-8.63-8.86-9.28l7.38-3.1S89 200 89 201s-2 5-4 5z\"/>\r\n            <path class=\"h\" d=\"M108.89 203.66c-.32-1.94-4.79-8.62-8.89-9.66l1.59-3.68c2.3.57 13.83 3.85 15.19 12z\"/>\r\n            <path class=\"h\" d=\"M110 197.89a35.18 35.18 0 00-9.08-1.9l.52-8a42.92 42.92 0 0111.64 2.49z\"/>\r\n            <path class=\"h\" d=\"M103.05 193.85l-1.58-7.85c1.16-.24 18.53-2 19.53 1s-1.19 5.44-3.59 6.72-11.79-.39-14.36.13zM105.19 177.85L97 174c.07 0 14.27-8.13 18.13-8.56s4.41 4.69 3.64 7.12-13.28 5.2-13.58 5.29zM73.19 194.18l-6.71-4.36C71.51 182.09 79 170 83 170a5.18 5.18 0 015.06 3.33l.08-.06c-1.77 1.43-9.14 11.97-14.95 20.91z\"/>\r\n            <path class=\"h\" d=\"M76 200.45l-6.32-4.9c7.26-9.37 15.64-20.76 16.52-22.83a.94.94 0 00-.06.18l7.69 2.2C93 178.19 83.65 190.59 76 200.45z\"/>\r\n            <path class=\"h\" d=\"M87.14 198.26l-6.6-4.52c5.62-8.21 11.9-18.59 12.31-21a1.77 1.77 0 000 .22h8c-.01 4.37-7.46 16.17-13.71 25.3z\"/>\r\n            <path class=\"h\" d=\"M96.05 198.18l-6.7-4.36c5.5-8.44 9.39-20.88 9.43-21l7.64 2.36c-.17.56-4.27 13.64-10.37 23z\"/>\r\n            <path class=\"h\" d=\"M102.05 198.18l-6.7-4.36c5.5-8.44 9.61-19.7 9.65-19.82l7 2c-.17.55-3.85 12.82-9.95 22.18z\"/>\r\n            </g>\r\n            <path fill=\"url(#b)\" d=\"M7.5 6.5h55v893h-55z\"/>\r\n            <path fill=\"url(#c)\" d=\"M846.5 6.5h55v893h-55z\"/>\r\n            <path class=\"k\" d=\"M7.5 140.5h56v63h-56zM7.5 330.5h56v63h-56zM7.5 515.5h56v63h-56zM7.5 701.5h56v63h-56z\"/>\r\n            <path class=\"l\" d=\"M8.51 143.16H59.5v24.34H8.51z\"/>\r\n            <path class=\"m\" d=\"M8.5 167.5h66v33h-66z\"/>\r\n            <path class=\"n\" d=\"M8.5 176.5h66v17h-66z\"/>\r\n            <path class=\"l\" d=\"M8.51 333.16H59.5v24.34H8.51z\"/>\r\n            <path class=\"o\" d=\"M8.5 357.5h66v33h-66z\"/>\r\n            <path class=\"p\" d=\"M8.5 366.5h66v17h-66z\"/>\r\n            <path class=\"l\" d=\"M8.51 518.16H59.5v24.34H8.51z\"/>\r\n            <path class=\"q\" d=\"M8.5 542.5h66v33h-66z\"/>\r\n            <path class=\"r\" d=\"M8.5 551.5h66v17h-66z\"/>\r\n            <path class=\"l\" d=\"M8.51 704.16H59.5v24.34H8.51z\"/>\r\n            <path class=\"s\" d=\"M8.5 728.5h66v33h-66z\"/>\r\n            <path class=\"t\" d=\"M8.5 737.5h66v17h-66z\"/>\r\n            <path class=\"k\" d=\"M845.5 142.5h56v63h-56z\"/>\r\n\r\n\r\n\r\n            <path class=\"o\" d=\"M794.5 169.5h108v33h-108z\" :style=\"data[game_change].color1_fio_1\"/>\r\n            <path class=\"p\" d=\"M794.5 178.5h108v17h-108z\" :style=\"data[game_change].color2_fio_1\"/>\r\n\r\n            \r\n\r\n            <path class=\"l light light-1\" d=\"M850.51 145.16h50.99v24.34h-50.99z\"/>\r\n            <path class=\"l light light-2\" d=\"M850.51 333.16h50.99v24.34h-50.99z\"/>\r\n            <path class=\"l light light-3\" d=\"M850.51 517.16h50.99v24.34h-50.99z\"/>\r\n            <path class=\"l light light-4\" d=\"M850.51 702.16h50.99v24.34h-50.99z\"/>\r\n            <path class=\"k\" d=\"M845.5 514.5h56v63h-56z\"/>\r\n            <path class=\"k\" d=\"M845.5 699.5h56v63h-56z\"/>\r\n            <path class=\"k\" d=\"M845.5 330.5h56v63h-56z\"/>\r\n\r\n\r\n\r\n\r\n            <path class=\"s\" d=\"M793.5 726.5h108v33h-108z\" :style=\"data[game_change].color1_fio_4\"/>\r\n            <path class=\"t\" d=\"M793.5 735.5h108v17h-108z\" :style=\"data[game_change].color2_fio_4\"/>\r\n\r\n            <path class=\"q\" d=\"M791.5 541.5h110v33h-110z\" :style=\"data[game_change].color1_fio_3\"/>\r\n            <path class=\"r\" d=\"M791.5 550.5h110v17h-110z\" :style=\"data[game_change].color2_fio_3\"/>\r\n\r\n            <path class=\"m\" d=\"M791.5 357.5h111v33h-111z\" :style=\"data[game_change].color1_fio_2\"/>\r\n            <path class=\"n\" d=\"M791.5 366.5h111v17h-111z\" :style=\"data[game_change].color2_fio_2\"/>\r\n\r\n\r\n\r\n\r\n            <path class=\"m\" d=\"M8.5 167.5h66v33h-66z\"/>\r\n            <path class=\"n\" d=\"M8.5 176.5h66v17h-66z\"/>\r\n            <g>\r\n            <path class=\"v\" d=\"M427.82 87.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V86.11l-12-.39s-4-14-22-14-19 14-19 14z\"/>\r\n            <circle class=\"w\" cx=\"431.78\" cy=\"97.72\" r=\"2\"/>\r\n            <circle class=\"w\" cx=\"484.78\" cy=\"97.72\" r=\"2\"/>\r\n            <path class=\"x\" d=\"M439.94 100.71l6-27-4 4-8 30 6-7zM476.94 107.71l-.12-21.99-5.88-10.01-2 27 8 5z\"/>\r\n            <path class=\"y\" d=\"M427.82 87.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V86.11l-12-.39s-4-14-22-14-19 14-19 14z\"/>\r\n            </g>\r\n            <g>\r\n            <path class=\"v\" d=\"M521.82 77.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V76.11l-12-.39s-4-14-22-14-19 14-19 14z\"/>\r\n            <circle class=\"w\" cx=\"525.78\" cy=\"87.72\" r=\"2\"/>\r\n            <circle class=\"w\" cx=\"578.78\" cy=\"87.72\" r=\"2\"/>\r\n            <path class=\"x\" d=\"M533.94 90.71l6-27-4 4-8 30 6-7zM570.94 97.71l-.12-21.99-5.88-10.01-2 27 8 5z\"/>\r\n            <path class=\"y\" d=\"M521.82 77.72l-1 20h9.28s4.72-14.37 19.22-13.72 20.36 13.69 20.36 13.69h15.1V76.11l-12-.39s-4-14-22-14-19 14-19 14z\"/>\r\n            </g>\r\n        </g>\r\n        <rect x=\"60\" y=\"165\" width=\"60\" height=\"40\" class=\"drag drag-1\" />\r\n        <rect x=\"60\" y=\"355\" width=\"60\" height=\"40\" class=\"drag drag-2\" />\r\n        <rect x=\"60\" y=\"540\" width=\"60\" height=\"40\" class=\"drag drag-3\" />\r\n        <rect x=\"60\" y=\"725\" width=\"60\" height=\"40\" class=\"drag drag-4\" />\r\n\r\n        <!-- DRAG LINES -->\r\n        <line x1=\"70\" y1=\"185\" x2=\"70\" y2=\"185\" class=\"line line-back line-1\" />\r\n        <line x1=\"70\" y1=\"185\" x2=\"70\" y2=\"185\" class=\"line line-1\" />\r\n        <line x1=\"65\" y1=\"375\" x2=\"65\" y2=\"375\" class=\"line line-back line-2\" />\r\n        <line x1=\"65\" y1=\"375\" x2=\"65\" y2=\"375\" class=\"line line-2\" />\r\n        <line x1=\"65\" y1=\"560\" x2=\"65\" y2=\"560\" class=\"line line-back line-3\" />\r\n        <line x1=\"65\" y1=\"560\" x2=\"65\" y2=\"560\" class=\"line line-3\" />\r\n        <line x1=\"65\" y1=\"745\" x2=\"65\" y2=\"745\" class=\"line line-back line-4\" />\r\n        <line x1=\"65\" y1=\"745\" x2=\"65\" y2=\"745\" class=\"line line-4\" />\r\n        <!-- DRAG LINES -->\r\n        </svg>\r\n    </div>\r\n</template>\r\n\r\n<script>\r\nimport gsap from \"gsap\"\r\nimport Draggable from \"gsap/Draggable\"\r\n\r\nimport axios from 'axios'\r\nexport default {\r\n    name: \"Mx_Fixing_Wiring\",\r\n    components: {\r\n        //\r\n    },\r\n\r\n    data() {\r\n        return {\r\n            NuiOpen: false,\r\n            size_game: 'width: 90vmin;',\r\n            position_nui: 'left: 50%; top: 50%; transform: translate(-50%, -50%) scale(1);',\r\n            name_resource: '',\r\n            sound_name: '',\r\n\r\n            game_change: 0,\r\n            data:[\r\n                {\r\n                    line1_color: 'blue', line1X: 670, line1Y: 188, light1: 2, \r\n                    line2_color: 'red', line2X: 670, line2Y: -188, light2: 1,\r\n                    line3_color: 'yellow', line3X: 670, line3Y: 0, light3: 3,\r\n                    line4_color: 'pink', line4X: 670, line4Y: 0, light4: 4,\r\n                    color1_fio_1: 'fill:#a71916', color2_fio_1: 'fill:#e52320', color1_fio_2: 'fill:#25378d', color2_fio_2: 'fill:#324d9c', \r\n                    color1_fio_3: 'fill:#aa9f17', color2_fio_3: 'fill:#ffeb13', color1_fio_4: 'fill:#90378c', color2_fio_4: 'fill:#a6529a',\r\n                },\r\n\r\n                {\r\n                    line1_color: 'blue', line1X: 670, line1Y: 0, light1: 1,\r\n                    line2_color: 'red', line2X: 670, line2Y: 185, light2: 3,\r\n                    line3_color: 'yellow', line3X: 670, line3Y: 185, light3: 4,\r\n                    line4_color: 'pink', line4X: 670, line4Y: -375, light4: 2,\r\n                    color1_fio_1: 'fill:#25378d', color2_fio_1: 'fill:#324d9c', color1_fio_2: 'fill:#90378c', color2_fio_2: 'fill:#a6529a', \r\n                    color1_fio_3: 'fill:#a71916', color2_fio_3: 'fill:#e52320', color1_fio_4: 'fill:#aa9f17', color2_fio_4: 'fill:#ffeb13',\r\n                },\r\n\r\n                {\r\n                    line1_color: 'blue', line1X: 670, line1Y: 0, light1: 1,\r\n                    line2_color: 'red', line2X: 670, line2Y: 370, light2: 4,\r\n                    line3_color: 'yellow', line3X: 670, line3Y: -190, light3: 2,\r\n                    line4_color: 'pink', line4X: 670, line4Y: -188, light4: 3,\r\n                    color1_fio_1: 'fill:#25378d', color2_fio_1: 'fill:#324d9c', color1_fio_2: 'fill:#aa9f17', color2_fio_2: 'fill:#ffeb13', \r\n                    color1_fio_3: 'fill:#90378c', color2_fio_3: 'fill:#a6529a', color1_fio_4: 'fill:#a71916', color2_fio_4: 'fill:#e52320',\r\n                },\r\n\r\n                {\r\n                    line1_color: 'blue', line1X: 670, line1Y: 568, light1: 4,\r\n                    line2_color: 'red', line2X: 670, line2Y: -188, light2: 1,\r\n                    line3_color: 'yellow', line3X: 670, line3Y: -188, light3: 2,\r\n                    line4_color: 'pink', line4X: 670, line4Y: -190, light4: 3,\r\n                    color1_fio_1: 'fill:#a71916', color2_fio_1: 'fill:#e52320', color1_fio_2: 'fill:#aa9f17', color2_fio_2: 'fill:#ffeb13', \r\n                    color1_fio_3: 'fill:#90378c', color2_fio_3: 'fill:#a6529a', color1_fio_4: 'fill:#25378d', color2_fio_4: 'fill:#324d9c',\r\n                },\r\n\r\n                {\r\n                    line1_color: 'blue', line1X: 670, line1Y: 380, light1: 3,\r\n                    line2_color: 'red', line2X: 670, line2Y: 0, light2: 2,\r\n                    line3_color: 'yellow', line3X: 670, line3Y: 188, light3: 4,\r\n                    line4_color: 'pink', line4X: 670, line4Y: -565, light4: 1,\r\n                    color1_fio_1: 'fill:#90378c', color2_fio_1: 'fill:#a6529a', color1_fio_2: 'fill:#a71916', color2_fio_2: 'fill:#e52320', \r\n                    color1_fio_3: 'fill:#25378d', color2_fio_3: 'fill:#324d9c', color1_fio_4: 'fill:#aa9f17', color2_fio_4: 'fill:#ffeb13',\r\n                },\r\n\r\n                {\r\n                    line1_color: 'blue', line1X: 670, line1Y: 188, light1: 2,\r\n                    line2_color: 'red', line2X: 670, line2Y: 375, light2: 4,\r\n                    line3_color: 'yellow', line3X: 670, line3Y: -375, light3: 1,\r\n                    line4_color: 'pink', line4X: 670, line4Y: -185, light4: 3,\r\n                    color1_fio_1: 'fill:#aa9f17', color2_fio_1: 'fill:#ffeb13', color1_fio_2: 'fill:#25378d', color2_fio_2: 'fill:#324d9c', \r\n                    color1_fio_3: 'fill:#90378c', color2_fio_3: 'fill:#a6529a', color1_fio_4: 'fill:#a71916', color2_fio_4: 'fill:#e52320',\r\n                },\r\n\r\n                {\r\n                    line1_color: 'blue', line1X: 670, line1Y: 0, light1: 1,\r\n                    line2_color: 'red', line2X: 670, line2Y: 188, light2: 3,\r\n                    line3_color: 'yellow', line3X: 670, line3Y: 188, light3: 4,\r\n                    line4_color: 'pink', line4X: 670, line4Y: -370, light4: 2,\r\n                    color1_fio_1: 'fill:#25378d', color2_fio_1: 'fill:#324d9c', color1_fio_2: 'fill:#90378c', color2_fio_2: 'fill:#a6529a', \r\n                    color1_fio_3: 'fill:#a71916', color2_fio_3: 'fill:#e52320', color1_fio_4: 'fill:#aa9f17', color2_fio_4: 'fill:#ffeb13',\r\n                },\r\n\r\n                // color1_fio_1: 'fill:#', color2_fio_1: 'fill:#', color1_fio_2: 'fill:#', color2_fio_2: 'fill:#', \r\n                // color1_fio_3: 'fill:#', color2_fio_3: 'fill:#', color1_fio_4: 'fill:#', color2_fio_4: 'fill:#',\r\n\r\n                // fill:#a71916 fill:#e52320  -- RED\r\n                // fill:#90378c fill:#a6529a  -- PINK\r\n                // fill:#aa9f17 fill:#ffeb13  -- YELLOW\r\n                // fill:#25378d fill:#324d9c  -- BLUE\r\n            ],\r\n        };\r\n    },\r\n\r\n    created: function () {\r\n        window.addEventListener(\"message\", this.receiveLua);\r\n        window.addEventListener('keydown', this.keyPress);\r\n    },\r\n\r\n    destroyed: function () {\r\n        window.removeEventListener(\"message\", this.receiveLua);\r\n        window.removeEventListener('keydown', this.keyPress);\r\n    },\r\n\r\n    // mounted() {\r\n    //     this.game_change = Math.floor(Math.random() * this.data.length)\r\n    //     gsap.registerPlugin(Draggable);\r\n    //     setTimeout(function() { \r\n    //         this.MountMinigame('asd', '1.ogg', this.data, this.game_change);\r\n    //     }.bind(this), 100);\r\n    // },\r\n\r\n    methods: {\r\n        SendToClient: function(to, data){\r\n            axios.post(`https://${this.name_resource}/${to}`, data)\r\n            .catch(msg => console.log(`An error occurred in: ${to} , ${msg}`));\r\n        },\r\n\r\n        keyPress: function(event){\r\n            const key = event.key;\r\n            if (key == 'Escape' || key == 'Backspace') {\r\n                this.SendToClient('CloseNui', {});\r\n            }\r\n        },\r\n\r\n        receiveLua: function (event) {\r\n            if (!event || !event.data) return;\r\n\r\n            const lua = event.data;\r\n\r\n            if(lua.ui == \"ui\") { \r\n                this.NuiOpen = lua.NuiOpen;\r\n                if (lua.NuiOpen) {\r\n                    this.position_nui = 'left: '+lua.x+'; top: '+lua.y+'; transform: translate(-'+lua.x+', -'+lua.y+') scale('+ lua.scale +');';\r\n                    this.size_game = 'width: '+ lua.size_game + ';';\r\n                    this.name_resource = lua.name_resource;\r\n                    this.sound_name = lua.sound_name;\r\n\r\n                    this.game_change = Math.floor(Math.random() * this.data.length)\r\n                    gsap.registerPlugin(Draggable);\r\n                    setTimeout(function() { \r\n                        this.MountMinigame(this.name_resource, this.sound_name, this.data, this.game_change);\r\n                    }.bind(this), 100);\r\n                }\r\n            } \r\n        },\r\n\r\n        MountMinigame(name_resource, sound_name, data, game_change) {\r\n            let completedLights = [0, 0, 0, 0];\r\n            new Draggable('.drag-1', {\r\n                onDrag: function () { updateLine('.line-1', this.x + 120, this.y + 185); },\r\n                onRelease: function () {\r\n                    if (this.x !== data[game_change].line1X || this.y !== data[game_change].line1Y ) {\r\n                        reset('.drag-1', '.line-1', 70, 185);\r\n                        toggleLight(data[game_change].light1, false);\r\n                    } else if (this.x === data[game_change].line1X && this.y === data[game_change].line1Y) toggleLight(data[game_change].light1, true)\r\n                },\r\n                liveSnap: {points: [{x: data[game_change].line1X, y: data[game_change].line1Y}],radius: 20}\r\n            });\r\n            \r\n            new Draggable('.drag-2', {\r\n                onDrag: function () { updateLine('.line-2', this.x + 120, this.y + 375); },\r\n                onRelease: function () {\r\n                    if (this.x !== data[game_change].line2X || this.y !== data[game_change].line2Y ) {\r\n                        reset('.drag-2', '.line-2', 60, 375);\r\n                        toggleLight(data[game_change].light2, false);\r\n                    } else if (this.x === data[game_change].line2X && this.y === data[game_change].line2Y) toggleLight(data[game_change].light2, true)\r\n                },\r\n                liveSnap: {points: [{x: data[game_change].line2X, y: data[game_change].line2Y}],radius: 20}\r\n            });\r\n            \r\n            new Draggable('.drag-3', {\r\n                onDrag: function () { updateLine('.line-3', this.x + 120, this.y + 560); },\r\n                onRelease: function () {\r\n                    if (this.x !== data[game_change].line3X || this.y !== data[game_change].line3Y ) {\r\n                        reset('.drag-3', '.line-3', 60, 560);\r\n                        toggleLight(data[game_change].light3, false);\r\n                    } else if (this.x === data[game_change].line3X && this.y === data[game_change].line3Y) toggleLight(data[game_change].light3, true)\r\n                },\r\n                liveSnap: {points: [{x: data[game_change].line3X, y: data[game_change].line3Y}],radius: 20}\r\n            });\r\n\r\n            new Draggable('.drag-4', {\r\n                onDrag: function () { updateLine('.line-4', this.x + 120, this.y + 745); },\r\n                onRelease: function () {\r\n                    if (this.x !== data[game_change].line4X || this.y !== data[game_change].line4Y ) {\r\n                        reset('.drag-4', '.line-4', 60, 745);\r\n                        toggleLight(data[game_change].light4, false);\r\n                    } else if (this.x === data[game_change].line4X && this.y === data[game_change].line4Y) toggleLight(data[game_change].light4, true)\r\n                },\r\n                liveSnap: {points: [{x: data[game_change].line4X, y: data[game_change].line4Y}],radius: 20}\r\n            });\r\n\r\n            function updateLine(selector, x, y) {\r\n                gsap.set(selector, {\r\n                    attr: {\r\n                    x2: x,\r\n                    y2: y\r\n                    }\r\n                });\r\n            }\r\n\r\n            function SendToClient2(to, data) {\r\n                axios.post(`https://${name_resource}/${to}`, data)\r\n                .catch(msg => console.log(`An error occurred in: ${to} , ${msg}`));\r\n            }\r\n\r\n            function toggleLight(selector, visibility) {\r\n                if (visibility) {\r\n                    completedLights[selector - 1] = 1;\r\n                    if (completedLights[0] === 1 && completedLights[1] === 1 && completedLights[2] === 1 && completedLights[3] === 1) {\r\n                        audioTask.play();\r\n                        window.setTimeout(() => {\r\n                            reset('.drag-1', '.line-1', 70, 185);\r\n                            reset('.drag-2', '.line-2', 60, 375);\r\n                            reset('.drag-3', '.line-3', 60, 560);\r\n                            reset('.drag-4', '.line-4', 60, 745);\r\n                            toggleLight(2, false);\r\n                            toggleLight(1, false);\r\n                            toggleLight(3, false);\r\n                            toggleLight(4, false);\r\n                            SendToClient2('electric_circuit_completed', {});\r\n                        }, 2000);\r\n                    }\r\n                } else {\r\n                    completedLights[selector - 1] = 0;\r\n                }\r\n                \r\n                gsap.to(`.light-${selector}`, {\r\n                    opacity: visibility ? 1 : 0,\r\n                    duration: 0.3\r\n                });\r\n            }\r\n\r\n            function reset(drag, line, x, y) {\r\n                gsap.to(drag, {\r\n                    duration: 0.3,\r\n                    ease: 'power2.out',\r\n                    x: 0,\r\n                    y: 0\r\n                });\r\n                gsap.to(line, {\r\n                    duration: 0.3,\r\n                    ease: 'power2.out',\r\n                    attr: {\r\n                    x2: x,\r\n                    y2: y\r\n                    }\r\n                });\r\n            }\r\n            const audioTask = new Audio(`sound/${sound_name}`);\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style>\r\nbody {\r\n    margin: 0;\r\n    display: grid;\r\n    place-items: center;\r\n    height: 100vh;\r\n    overflow: hidden;\r\n}\r\n#container{\r\n    position:absolute;\r\n\ttop: 0%;\r\n\tleft: 0%;\r\n\ttransform: translate(-0%, -0%) scale(1.0);\r\n}\r\nsvg {\r\n    height: auto;\r\n    border-radius: 2vh;\r\n}\r\n\r\n.light {\r\n    opacity: 0; \r\n}\r\n\r\n.drag {fill: white; opacity: 0;}\r\n.line {\r\n    stroke-width: 18px;\r\n    pointer-events: none;\r\n}\r\n.line-back {\r\n    stroke-width: 30px;\r\n    pointer-events: none;\r\n}\r\n.line-1 {\r\n    stroke: #324d9c;\r\n}\r\n.line-2 {\r\n    stroke: #e52320;\r\n}\r\n.line-3 {\r\n    stroke: #ffeb13;\r\n}\r\n.line-4 {\r\n    stroke: #a6529a;\r\n}\r\n\r\n.c{fill:#273065;stroke:#1a1b36}.c,.d,.e,.f,.k,.u{stroke-miterlimit:10}.c,.d,.e,.f,.u,.y{stroke-width:5px}.d{fill:#71160e;stroke:#280f10}.e{fill:#8c6c15}.e,.u{stroke:#38321a}.f{fill:#212021;stroke:#000}.h{fill:#9b3015;stroke:#471d12}.h,.y{stroke-linecap:round;stroke-linejoin:round}.k,.y{fill:none}.k{stroke:#1d1d1b;stroke-width:6px}.l{fill:#d9c905}.m{fill:#25378d}.n{fill:#324d9c}.o{fill:#a71916}.p{fill:#e52320}.q{fill:#aa9f17}.r{fill:#ffeb13}.s{fill:#90378c}.t{fill:#a6529a}.u{fill:#1d1d1b}.v{fill:#5b5c64}.w{fill:#292829}.x{fill:#2f3038}.y{stroke:#252526}\r\n@import url('https://fonts.googleapis.com/css2?family=Bree+Serif&display=swap');\r\n</style>\r\n\r\n", "import mod from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../node_modules/cache-loader/dist/cjs.js??ref--12-0!../node_modules/thread-loader/dist/cjs.js!../node_modules/babel-loader/lib/index.js!../node_modules/cache-loader/dist/cjs.js??ref--0-0!../node_modules/vue-loader/lib/index.js??vue-loader-options!./App.vue?vue&type=script&lang=js&\"", "import { render, staticRenderFns } from \"./App.vue?vue&type=template&id=70a7890e&\"\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null\n  \n)\n\nexport default component.exports", "import Vue from 'vue'\nimport App from './App.vue'\n\nVue.config.productionTip = false\n\nnew Vue({\n  render: h => h(App),\n}).$mount('#app')\n"], "sourceRoot": ""}