-- ===========================
-- GLOBÁLNÍ PROMĚNNÉ
-- ===========================

local activeMinigames = {}
local registeredCallbacks = {}

-- ===========================
-- UTILITY FUNKCE
-- ===========================

-- Debug print funkce
local function debugPrint(message)
    if Config.Debug then
        print("[WiringMinigame-Server] " .. tostring(message))
    end
end

-- Získání hráče podle source
local function getPlayer(source)
    if Config.UseESX then
        local ESX = exports['es_extended']:getSharedObject()
        return ESX.GetPlayerFromId(source)
    elseif Config.UseQBCore then
        local QBCore = exports['qb-core']:GetCoreObject()
        return QBCore.Functions.GetPlayer(source)
    else
        return {
            source = source,
            identifier = GetPlayerIdentifier(source, 0) or tostring(source),
            name = GetPlayerName(source) or "Unknown"
        }
    end
end

-- Validace hráče
local function isValidPlayer(source)
    return source and GetPlayerName(source) and GetPlayerPing(source) > 0
end

-- ===========================
-- SPRÁVA AKTIVNÍCH MINIHER
-- ===========================

-- Přidání aktivní minihry
local function addActiveMinigame(source, data)
    if #activeMinigames >= Config.MaxConcurrentGames then
        debugPrint("Maximum concurrent games reached!")
        return false
    end
    
    activeMinigames[source] = {
        playerId = source,
        startTime = os.time(),
        data = data or {}
    }
    
    debugPrint("Added active minigame for player " .. source)
    return true
end

-- Odebrání aktivní minihry
local function removeActiveMinigame(source)
    if activeMinigames[source] then
        activeMinigames[source] = nil
        debugPrint("Removed active minigame for player " .. source)
        return true
    end
    return false
end

-- Kontrola aktivní minihry
local function isMinigameActive(source)
    return activeMinigames[source] ~= nil
end

-- ===========================
-- CALLBACK SYSTÉM
-- ===========================

-- Registrace callback funkce
local function registerCallback(eventName, callback)
    if not registeredCallbacks[eventName] then
        registeredCallbacks[eventName] = {}
    end
    
    table.insert(registeredCallbacks[eventName], callback)
    debugPrint("Registered callback for event: " .. eventName)
end

-- Volání callback funkcí
local function triggerCallbacks(eventName, ...)
    if registeredCallbacks[eventName] then
        for _, callback in ipairs(registeredCallbacks[eventName]) do
            local success, error = pcall(callback, ...)
            if not success then
                debugPrint("Error in callback for " .. eventName .. ": " .. tostring(error))
            end
        end
    end
end

-- ===========================
-- UDÁLOSTI
-- ===========================

-- Dokončení minihry
RegisterNetEvent('wiring_minigame:completed')
AddEventHandler('wiring_minigame:completed', function(playerId)
    local source = source
    
    if not isValidPlayer(source) then
        debugPrint("Invalid player tried to complete minigame: " .. tostring(source))
        return
    end
    
    if not isMinigameActive(source) then
        debugPrint("Player " .. source .. " completed minigame but wasn't active")
        return
    end
    
    local player = getPlayer(source)
    local minigameData = activeMinigames[source]
    
    debugPrint("Player " .. source .. " completed wiring minigame")
    
    -- Výpočet času dokončení
    local completionTime = os.time() - minigameData.startTime
    
    -- Trigger callbacks
    triggerCallbacks('onComplete', source, player, completionTime, minigameData.data)
    
    -- Výchozí callback
    if Config.DefaultCallbacks.onComplete then
        Config.DefaultCallbacks.onComplete(source)
    end
    
    -- Odebrání z aktivních
    removeActiveMinigame(source)
    
    -- Log pro administrátory
    TriggerEvent('wiring_minigame:log', {
        type = 'completed',
        player = source,
        playerName = GetPlayerName(source),
        time = completionTime,
        timestamp = os.date('%Y-%m-%d %H:%M:%S')
    })
end)

-- Selhání minihry
RegisterNetEvent('wiring_minigame:failed')
AddEventHandler('wiring_minigame:failed', function(reason)
    local source = source
    
    if not isValidPlayer(source) then
        return
    end
    
    if not isMinigameActive(source) then
        return
    end
    
    local player = getPlayer(source)
    local minigameData = activeMinigames[source]
    
    debugPrint("Player " .. source .. " failed wiring minigame: " .. tostring(reason))
    
    -- Trigger callbacks
    triggerCallbacks('onFail', source, player, reason, minigameData.data)
    
    -- Výchozí callback
    if Config.DefaultCallbacks.onFail then
        Config.DefaultCallbacks.onFail(source, reason)
    end
    
    -- Odebrání z aktivních
    removeActiveMinigame(source)
end)

-- Zavření minihry
RegisterNetEvent('wiring_minigame:closed')
AddEventHandler('wiring_minigame:closed', function()
    local source = source
    
    if not isValidPlayer(source) then
        return
    end
    
    if isMinigameActive(source) then
        local player = getPlayer(source)
        local minigameData = activeMinigames[source]
        
        debugPrint("Player " .. source .. " closed wiring minigame")
        
        -- Trigger callbacks
        triggerCallbacks('onClose', source, player, minigameData.data)
        
        -- Odebrání z aktivních
        removeActiveMinigame(source)
    end
end)

-- ===========================
-- EXPORTY
-- ===========================

-- Export pro registraci callback funkce
exports('RegisterMinigameCallback', function(eventName, callback)
    if type(eventName) ~= 'string' or type(callback) ~= 'function' then
        debugPrint("Invalid parameters for RegisterMinigameCallback")
        return false
    end
    
    registerCallback(eventName, callback)
    return true
end)

-- Export pro spuštění minihry pro hráče
exports('StartMinigameForPlayer', function(source, options, data)
    if not isValidPlayer(source) then
        debugPrint("Invalid player for StartMinigameForPlayer: " .. tostring(source))
        return false
    end
    
    if isMinigameActive(source) then
        debugPrint("Player " .. source .. " already has active minigame")
        return false
    end
    
    -- Přidání do aktivních
    if not addActiveMinigame(source, data) then
        return false
    end
    
    -- Spuštění na klientovi
    TriggerClientEvent('wiring_minigame:start', source, options)
    
    debugPrint("Started minigame for player " .. source)
    return true
end)

-- Export pro zastavení minihry
exports('StopMinigameForPlayer', function(source)
    if not isValidPlayer(source) then
        return false
    end
    
    if isMinigameActive(source) then
        TriggerClientEvent('wiring_minigame:stop', source)
        removeActiveMinigame(source)
        return true
    end
    
    return false
end)

-- Export pro získání aktivních miniher
exports('GetActiveMinigames', function()
    return activeMinigames
end)

-- Export pro kontrolu aktivity
exports('IsPlayerInMinigame', function(source)
    return isMinigameActive(source)
end)

-- ===========================
-- ADMIN PŘÍKAZY
-- ===========================

-- Příkaz pro zobrazení aktivních miniher
RegisterCommand('wiring_active', function(source, args, rawCommand)
    if source == 0 or IsPlayerAceAllowed(source, 'wiring.admin') then
        local count = 0
        for _ in pairs(activeMinigames) do
            count = count + 1
        end
        
        print("Active wiring minigames: " .. count .. "/" .. Config.MaxConcurrentGames)
        
        for playerId, data in pairs(activeMinigames) do
            local playerName = GetPlayerName(playerId) or "Unknown"
            local duration = os.time() - data.startTime
            print("  - Player " .. playerId .. " (" .. playerName .. ") - Duration: " .. duration .. "s")
        end
    end
end, false)

-- ===========================
-- CLEANUP
-- ===========================

-- Cleanup při odpojení hráče
AddEventHandler('playerDropped', function(reason)
    local source = source
    removeActiveMinigame(source)
    debugPrint("Cleaned up minigame for disconnected player " .. source)
end)

-- ===========================
-- INICIALIZACE
-- ===========================

Citizen.CreateThread(function()
    debugPrint("Wiring Minigame Server initialized")
    debugPrint("Max concurrent games: " .. Config.MaxConcurrentGames)
    debugPrint("Debug mode: " .. tostring(Config.Debug))
end)
