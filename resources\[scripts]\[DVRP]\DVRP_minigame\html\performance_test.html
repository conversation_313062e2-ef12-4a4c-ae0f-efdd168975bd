<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Performance Test - Wiring Minigame</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
            color: white;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        
        .test-button:hover {
            background: #45a049;
        }
        
        .performance-info {
            background: #333;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .fps-counter {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
        }
        
        iframe {
            width: 100%;
            height: 600px;
            border: 2px solid #555;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 Performance Test - Wiring Minigame</h1>
        
        <div class="performance-info">
            <h3>📊 Performance Optimalizace:</h3>
            <ul>
                <li>✅ RequestAnimationFrame throttling pro drag events</li>
                <li>✅ Cachování DOM elementů</li>
                <li>✅ Optimalizované CSS transitions</li>
                <li>✅ Hardware acceleration (GPU)</li>
                <li>✅ Přímá SVG manipulace místo GSAP během drag</li>
                <li>✅ Odstranění transitions během drag operací</li>
            </ul>
        </div>
        
        <div class="fps-counter" id="fpsCounter">
            FPS: <span id="fpsValue">--</span><br>
            Frame Time: <span id="frameTime">--</span>ms
        </div>
        
        <button class="test-button" onclick="loadOriginal()">🔴 Načíst Původní Verzi</button>
        <button class="test-button" onclick="loadOptimized()">🟢 Načíst Optimalizovanou Verzi</button>
        <button class="test-button" onclick="startPerformanceTest()">⚡ Spustit Performance Test</button>
        
        <div id="testResults" class="performance-info" style="display: none;">
            <h3>📈 Výsledky testu:</h3>
            <div id="resultsContent"></div>
        </div>
        
        <iframe id="gameFrame" src="about:blank"></iframe>
    </div>

    <script>
        let fpsCounter = 0;
        let lastTime = performance.now();
        let frameCount = 0;
        
        function updateFPS() {
            const now = performance.now();
            const delta = now - lastTime;
            frameCount++;
            
            if (delta >= 1000) {
                const fps = Math.round((frameCount * 1000) / delta);
                const frameTime = Math.round(delta / frameCount * 100) / 100;
                
                document.getElementById('fpsValue').textContent = fps;
                document.getElementById('frameTime').textContent = frameTime;
                
                frameCount = 0;
                lastTime = now;
            }
            
            requestAnimationFrame(updateFPS);
        }
        
        updateFPS();
        
        function loadOriginal() {
            document.getElementById('gameFrame').src = 'new_index.html';
            showMessage('Načtena původní verze minihry');
        }
        
        function loadOptimized() {
            document.getElementById('gameFrame').src = 'simple_enhanced.html';
            showMessage('Načtena optimalizovaná verze minihry');
        }
        
        function startPerformanceTest() {
            const results = document.getElementById('testResults');
            const content = document.getElementById('resultsContent');
            
            content.innerHTML = `
                <p><strong>🧪 Test probíhá...</strong></p>
                <p>1. Načtěte jednu z verzí minihry</p>
                <p>2. Zkuste spojovat kabely a sledujte FPS counter</p>
                <p>3. Porovnejte plynulost mezi verzemi</p>
                <p><em>Optimalizovaná verze by měla mít stabilnější FPS během drag operací</em></p>
            `;
            
            results.style.display = 'block';
        }
        
        function showMessage(message) {
            const temp = document.createElement('div');
            temp.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: #4CAF50;
                color: white;
                padding: 15px 25px;
                border-radius: 5px;
                z-index: 1000;
                font-weight: bold;
            `;
            temp.textContent = message;
            document.body.appendChild(temp);
            
            setTimeout(() => {
                document.body.removeChild(temp);
            }, 2000);
        }
        
        // Simulace dat pro minihru
        window.addEventListener('message', function(event) {
            if (event.data.type === 'ready') {
                // Pošleme testovací data do minihry
                const iframe = document.getElementById('gameFrame');
                iframe.contentWindow.postMessage({
                    type: 'start_minigame',
                    data: {
                        wire_count: 6,
                        time_limit: 30,
                        snap_radius: 50,
                        sound_name: '1.ogg'
                    }
                }, '*');
            }
        });
    </script>
</body>
</html>
