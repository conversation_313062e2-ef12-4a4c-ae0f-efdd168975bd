-- ===========================
-- PŘÍKLAD POUŽITÍ WIRING MINIGAME
-- ===========================

-- Tento soubor obsahuje příklady použití minihry spojování kabelů
-- Můžete ho použít jako referenci nebo ho přidat do svého resource

-- ===========================
-- ZÁKLADNÍ PŘÍKLADY
-- ===========================

-- POZNÁMKA: Pokud máte povolený test command v config.lua, můžete použít:
-- /wiring_test [počet_kabelů] [barvy] [poloměr_přichycení] [měřítko]
-- Například: /wiring_test 6 rainbow 15 1.2

-- Příklad 1: <PERSON><PERSON>lad<PERSON><PERSON> spuštěn<PERSON> minihry
RegisterCommand('wiring_basic', function(source, args)
    exports['wiring_minigame']:StartWiringMinigame()
end)

-- Příklad 2: Minigra s vlastním nastavením
RegisterCommand('wiring_custom', function(source, args)
    local options = {
        wire_count = 6,
        colors = "extended",
        snap_radius = 20,
        scale = 1.2
    }
    
    local callbacks = {
        onComplete = function(playerId)
            TriggerEvent('chat:addMessage', {
                args = {'SYSTÉM', 'Minigra dokončena! Gratulujeme!'}
            })
        end,
        
        onFail = function(playerId, reason)
            TriggerEvent('chat:addMessage', {
                args = {'SYSTÉM', 'Minigra selhala: ' .. (reason or 'neznámý důvod')}
            })
        end
    }
    
    exports['wiring_minigame']:StartWiringMinigame(options, callbacks)
end)

-- Příklad 3: Použití presetů obtížnosti
RegisterCommand('wiring_easy', function(source, args)
    exports['wiring_minigame']:StartWiringMinigame(Config.Presets.easy)
end)

RegisterCommand('wiring_hard', function(source, args)
    exports['wiring_minigame']:StartWiringMinigame(Config.Presets.hard)
end)

RegisterCommand('wiring_insane', function(source, args)
    exports['wiring_minigame']:StartWiringMinigame(Config.Presets.insane)
end)

-- ===========================
-- POKROČILÉ PŘÍKLADY
-- ===========================

-- Příklad 4: Integrace s inventářem (ESX)
RegisterCommand('hack_panel', function(source, args)
    -- Zkontrolujte, zda má hráč potřebný předmět
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if xPlayer.getInventoryItem('hacking_device').count > 0 then
        local options = {
            wire_count = 8,
            colors = "rainbow",
            snap_radius = 15
        }
        
        local callbacks = {
            onComplete = function(playerId)
                -- Odměna za úspěšný hack
                xPlayer.addMoney(5000)
                xPlayer.removeInventoryItem('hacking_device', 1)
                xPlayer.showNotification('Hack úspěšný! Získali jste $5000')
                
                -- Trigger další event
                TriggerEvent('security:panel_hacked', source)
            end,
            
            onFail = function(playerId, reason)
                -- Trest za neúspěšný hack
                xPlayer.removeInventoryItem('hacking_device', 1)
                xPlayer.showNotification('Hack selhal! Zařízení zničeno')
                
                -- Možná upozornění policie
                TriggerEvent('police:alert', source, 'Pokus o hack')
            end
        }
        
        exports['wiring_minigame']:StartWiringMinigame(options, callbacks)
    else
        xPlayer.showNotification('Potřebujete hacking device!')
    end
end)

-- Příklad 5: Minigra s časovým limitem
RegisterCommand('wiring_timed', function(source, args)
    local timeLimit = tonumber(args[1]) or 30
    
    local options = {
        wire_count = 5,
        colors = "classic",
        time_limit = timeLimit
    }
    
    local callbacks = {
        onComplete = function(playerId)
            TriggerEvent('chat:addMessage', {
                args = {'SYSTÉM', 'Dokončeno včas! Bonus za rychlost!'}
            })
        end,
        
        onFail = function(playerId, reason)
            if reason == "timeout" then
                TriggerEvent('chat:addMessage', {
                    args = {'SYSTÉM', 'Čas vypršel! Zkuste to znovu.'}
                })
            end
        end
    }
    
    exports['wiring_minigame']:StartWiringMinigame(options, callbacks)
end)

-- ===========================
-- SERVER-SIDE PŘÍKLADY
-- ===========================

-- Příklad 6: Server-side spuštění minihry
RegisterCommand('admin_wiring', function(source, args)
    local targetId = tonumber(args[1])
    
    if targetId and GetPlayerName(targetId) then
        local options = {
            wire_count = 4,
            colors = "classic"
        }
        
        exports['wiring_minigame']:StartMinigameForPlayer(targetId, options)
        
        TriggerClientEvent('chat:addMessage', source, {
            args = {'ADMIN', 'Minigra spuštěna pro hráče ' .. GetPlayerName(targetId)}
        })
    else
        TriggerClientEvent('chat:addMessage', source, {
            args = {'CHYBA', 'Neplatné ID hráče'}
        })
    end
end, true) -- Pouze pro administrátory

-- Příklad 7: Registrace globálních callback funkcí
exports['wiring_minigame']:RegisterMinigameCallback('onComplete', function(source, player, completionTime, data)
    print("Hráč " .. GetPlayerName(source) .. " dokončil minihru za " .. completionTime .. " sekund")
    
    -- Statistiky
    TriggerEvent('player:updateStats', source, 'minigames_completed', 1)
    
    -- Bonus za rychlé dokončení
    if completionTime < 20 then
        TriggerClientEvent('chat:addMessage', source, {
            args = {'BONUS', 'Rychlostní bonus! +$500'}
        })
        -- Přidat peníze podle vašeho frameworku
    end
end)

-- ===========================
-- UTILITY FUNKCE
-- ===========================

-- Funkce pro kontrolu, zda může hráč spustit minihru
function CanPlayerStartMinigame(source)
    -- Zkontrolujte různé podmínky
    if exports['wiring_minigame']:IsMinigameActive() then
        return false, "Minigra již běží"
    end
    
    -- Další kontroly...
    local playerPed = GetPlayerPed(source)
    if IsPedInAnyVehicle(playerPed, false) then
        return false, "Nemůžete být ve vozidle"
    end
    
    return true, "OK"
end

-- Příklad použití utility funkce
RegisterCommand('safe_wiring', function(source, args)
    local canStart, reason = CanPlayerStartMinigame(source)
    
    if canStart then
        exports['wiring_minigame']:StartWiringMinigame()
    else
        TriggerClientEvent('chat:addMessage', source, {
            args = {'CHYBA', reason}
        })
    end
end)

-- ===========================
-- EVENTY PRO INTEGRACI
-- ===========================

-- Event pro spuštění minihry z jiných resources
RegisterNetEvent('wiring:start_custom')
AddEventHandler('wiring:start_custom', function(difficulty, reward)
    local source = source
    local preset = Config.Presets[difficulty] or Config.Presets.normal
    
    local callbacks = {
        onComplete = function(playerId)
            -- Přidat odměnu podle parametru
            TriggerEvent('economy:addMoney', source, reward or 1000)
        end
    }
    
    exports['wiring_minigame']:StartMinigameForPlayer(source, preset)
end)

-- ===========================
-- ADMIN PŘÍKAZY
-- ===========================

-- Zobrazení aktivních miniher
RegisterCommand('wiring_status', function(source, args)
    if source == 0 or IsPlayerAceAllowed(source, 'wiring.admin') then
        local activeGames = exports['wiring_minigame']:GetActiveMinigames()
        local count = 0
        
        for _ in pairs(activeGames) do
            count = count + 1
        end
        
        print("Aktivní minihry: " .. count)
        
        for playerId, data in pairs(activeGames) do
            local playerName = GetPlayerName(playerId) or "Unknown"
            local duration = os.time() - data.startTime
            print("  - " .. playerName .. " (" .. playerId .. ") - " .. duration .. "s")
        end
    end
end, false)
