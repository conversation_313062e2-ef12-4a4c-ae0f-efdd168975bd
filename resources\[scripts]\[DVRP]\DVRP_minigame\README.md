# 🔌 FiveM Wiring Minigame

Pokročilá minigra spojování kabelů inspirovaná Among Us pro FiveM servery. P<PERSON><PERSON> konfigurovatelná s podporou až 32 kabel<PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON> barevných palet a obtížností.

## 📋 Obsah

- [Instalace](#instalace)
- [Konfigurace](#konfigurace)
- [Exporty](#exporty)
- [Příklady použití](#příklady-použití)
- [API Reference](#api-reference)
- [Troubleshooting](#troubleshooting)

## 🚀 Instalace

1. Stáhněte nebo naklonujte tento resource do složky `resources` vašeho FiveM serveru
2. Přejmenujte složku na `wiring_minigame` (nebo jiný název dle preference)
3. Přidejte `ensure wiring_minigame` do vašeho `server.cfg`
4. Restartujte server

## ⚙️ Konfigurace

Všechna nastavení najdete v souboru `config.lua`:

### <PERSON><PERSON><PERSON><PERSON><PERSON> nastaven<PERSON>
```lua
Config.WireCount = 4          -- <PERSON>č<PERSON> kabel<PERSON> (1-32)
Config.Colors = "auto"        -- Barevná paleta
Config.SnapRadius = 25        -- Poloměr přichycení
Config.TimeLimit = 0          -- Časový limit (0 = neomezeno)
```

### Barevné palety
- `"auto"` - Automatická volba podle počtu kabelů
- `"classic"` - Klasické 4 barvy (modrá, červená, žlutá, fialová)
- `"extended"` - Rozšířená paleta pro více kabelů
- `"rainbow"` - Duha barev pro velké množství kabelů
- Vlastní pole: `{"#ff0000", "#00ff00", "#0000ff", ...}`

### Preset konfigurace
```lua
-- Použití presetu
local options = Config.Presets.hard
exports['wiring_minigame']:StartWiringMinigame(options)
```

Dostupné presety: `easy`, `normal`, `hard`, `expert`, `insane`

## 📦 Exporty

### Client-side exporty

#### StartWiringMinigame
Spustí minihru pro aktuálního hráče.

```lua
exports['wiring_minigame']:StartWiringMinigame(options, callbacks)
```

**Parametry:**
- `options` (table, volitelné) - Nastavení minihry
- `callbacks` (table, volitelné) - Callback funkce

**Návratová hodnota:** `boolean` - true pokud se minigra spustila úspěšně

#### IsMinigameActive
Zkontroluje, zda je minigra aktivní.

```lua
local isActive = exports['wiring_minigame']:IsMinigameActive()
```

#### StopMinigame
Zastaví aktivní minihru.

```lua
exports['wiring_minigame']:StopMinigame()
```

### Server-side exporty

#### StartMinigameForPlayer
Spustí minihru pro konkrétního hráče.

```lua
exports['wiring_minigame']:StartMinigameForPlayer(source, options, data)
```

#### RegisterMinigameCallback
Registruje callback funkci pro události minihry.

```lua
exports['wiring_minigame']:RegisterMinigameCallback(eventName, callback)
```

**Dostupné události:**
- `onComplete` - Dokončení minihry
- `onFail` - Selhání minihry
- `onClose` - Zavření minihry

## 💡 Příklady použití

### Základní použití
```lua
-- Spuštění s výchozím nastavením
exports['wiring_minigame']:StartWiringMinigame()
```

### Pokročilé použití s callbacks
```lua
local options = {
    wire_count = 6,
    colors = "extended",
    snap_radius = 20
}

local callbacks = {
    onComplete = function(playerId)
        print("Hráč " .. playerId .. " dokončil minihru!")
        -- Zde můžete přidat odměnu
    end,
    
    onFail = function(playerId, reason)
        print("Hráč " .. playerId .. " neuspěl: " .. reason)
    end
}

exports['wiring_minigame']:StartWiringMinigame(options, callbacks)
```

### Integrace s ESX
```lua
-- V config.lua nastavte Config.UseESX = true

RegisterCommand('hackpanel', function(source, args)
    local xPlayer = ESX.GetPlayerFromId(source)
    
    if xPlayer.getInventoryItem('hacking_device').count > 0 then
        local options = {
            wire_count = 8,
            colors = "rainbow"
        }
        
        local callbacks = {
            onComplete = function(playerId)
                xPlayer.addMoney(5000)
                xPlayer.showNotification('Hack úspěšný! +$5000')
            end
        }
        
        TriggerClientEvent('wiring_minigame:start', source, options, callbacks)
    end
end)
```

### Server-side callback registrace
```lua
-- V server.lua jiného resource
exports['wiring_minigame']:RegisterMinigameCallback('onComplete', function(source, player, completionTime, data)
    print("Hráč " .. GetPlayerName(source) .. " dokončil minihru za " .. completionTime .. " sekund")
    
    -- Přidání odměny podle času
    if completionTime < 30 then
        -- Rychlé dokončení - bonus
        TriggerClientEvent('chat:addMessage', source, {
            args = {'SYSTÉM', 'Rychlostní bonus! +$1000'}
        })
    end
end)
```

## 🎮 Ovládání

- **Myš** - Táhnutí kabelů
- **ESC/Backspace** - Zavření minihry (pokud povoleno v konfiguraci)

## 🧪 Test Command

Pro rychlé testování minihry můžete použít vestavěný test command:

```
/wiring_test [počet_kabelů] [barvy] [poloměr_přichycení] [měřítko]
```

**Příklady:**
```
/wiring_test                  # Spustí s výchozím nastavením
/wiring_test 6                # Spustí s 6 kabely
/wiring_test 8 rainbow 15 1.2 # 8 kabelů, duha barev, přichycení 15px, měřítko 1.2
```

Test command lze konfigurovat v `config.lua`:
```lua
Config.EnableTestCommand = true        -- Povolit/zakázat test command
Config.TestCommandName = "wiring_test" -- Název příkazu
Config.TestCommandPermission = nil     -- Oprávnění (nil = všichni, "admin" = pouze admini)
```

## 🔧 API Reference

### Options parametry

| Parametr | Typ | Výchozí | Popis |
|----------|-----|---------|-------|
| `wire_count` | number | 4 | Počet kabelů (1-32) |
| `colors` | string/table | "auto" | Barevná paleta |
| `snap_radius` | number | 25 | Poloměr přichycení v pixelech |
| `scale` | number | 1.0 | Měřítko minihry |
| `size_game` | string | "907px" | Velikost herního okna |

### Callback funkce

```lua
callbacks = {
    onComplete = function(playerId) end,
    onFail = function(playerId, reason) end,
    onClose = function(playerId) end
}
```

## 🐛 Troubleshooting

### Minigra se nespustí
- Zkontrolujte, zda je resource správně nainstalován a spuštěn
- Ověřte, že nejsou chyby v F8 konzoli
- Ujistěte se, že používáte správnou syntaxi exportů

### Zvuky nefungují
- Zkontrolujte, zda jsou zvukové soubory ve složce `html/sound/`
- Ověřte nastavení `Config.EnableSounds = true`
- Zkontrolujte název souboru v `Config.CompletionSound`

### Problémy s výkonem
- Snižte počet kabelů pro slabší klienty
- Upravte `Config.MaxConcurrentGames` na serveru
- Zkontrolujte, zda není spuštěno příliš mnoho miniher současně

## 📝 Changelog

### v1.0.0
- Počáteční vydání
- Podpora až 32 kabelů
- Konfigurovatelné barevné palety
- Preset obtížnosti
- Kompletní callback systém
- ESX/QB-Core integrace

## 📄 Licence

Tento projekt je licencován pod MIT licencí.

## 🤝 Podpora

Pro podporu a hlášení chyb vytvořte issue v GitHub repozitáři nebo kontaktujte autora.

---

**Vytvořeno s ❤️ pro FiveM komunitu**
