// Enhanced mx_fixwiring with configurable wire count and colors
// Supports 1-32 wires with dynamic positioning and colors

import { gsap } from 'gsap';
import { Draggable } from 'gsap/Draggable';
import axios from 'axios';

// Color palettes for different wire counts
const COLOR_PALETTES = {
    classic: ["#324d9c", "#e52320", "#ffeb13", "#a6529a"],
    extended: ["#324d9c", "#e52320", "#ffeb13", "#a6529a", "#00ff00", "#ff8800", "#8800ff", "#00ffff"],
    rainbow: [
        "#ff0000", "#ff8800", "#ffff00", "#88ff00", "#00ff00", "#00ff88", 
        "#00ffff", "#0088ff", "#0000ff", "#8800ff", "#ff00ff", "#ff0088",
        "#ff4444", "#ffaa44", "#ffff44", "#aaff44", "#44ff44", "#44ffaa",
        "#44ffff", "#44aaff", "#4444ff", "#aa44ff", "#ff44ff", "#ff44aa",
        "#880000", "#884400", "#888800", "#448800", "#008800", "#008844",
        "#008888", "#004488", "#000088", "#440088", "#880088", "#880044"
    ]
};

// Generate automatic colors using HSL
function generateAutoColors(count) {
    const colors = [];
    for (let i = 0; i < count; i++) {
        const hue = (i * 360 / count) % 360;
        const saturation = 70 + (i % 3) * 10; // 70%, 80%, 90%
        const lightness = 45 + (i % 2) * 10;  // 45%, 55%
        colors.push(`hsl(${hue}, ${saturation}%, ${lightness}%)`);
    }
    return colors;
}

// Get colors based on configuration
function getColors(colorConfig, wireCount) {
    if (Array.isArray(colorConfig)) {
        return colorConfig.slice(0, wireCount);
    }
    
    switch (colorConfig) {
        case "classic":
            return COLOR_PALETTES.classic.slice(0, wireCount);
        case "extended":
            return COLOR_PALETTES.extended.slice(0, wireCount);
        case "rainbow":
            return COLOR_PALETTES.rainbow.slice(0, wireCount);
        case "auto":
        default:
            if (wireCount <= 4) return COLOR_PALETTES.classic.slice(0, wireCount);
            if (wireCount <= 8) return COLOR_PALETTES.extended.slice(0, wireCount);
            if (wireCount <= 32) return COLOR_PALETTES.rainbow.slice(0, wireCount);
            return generateAutoColors(wireCount);
    }
}

// Calculate wire positions based on count
function calculateWirePositions(wireCount, gameWidth = 907, gameHeight = 907) {
    const leftSide = [];
    const rightSide = [];
    
    // Calculate spacing
    const topMargin = 140;
    const bottomMargin = 140;
    const availableHeight = gameHeight - topMargin - bottomMargin;
    const spacing = availableHeight / (wireCount + 1);
    
    for (let i = 0; i < wireCount; i++) {
        const y = topMargin + spacing * (i + 1);
        leftSide.push({
            x: 60,
            y: y,
            width: 60,
            height: Math.min(40, availableHeight / wireCount * 0.8)
        });
        rightSide.push({
            x: gameWidth - 120,
            y: y - 12,
            width: 108,
            height: 33
        });
    }
    
    return { leftSide, rightSide };
}

// Generate random configuration
function generateRandomConfiguration(wireCount, colors) {
    const wires = [];
    const targetLights = [...Array(wireCount).keys()];
    
    // Shuffle target lights for random connections
    for (let i = targetLights.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [targetLights[i], targetLights[j]] = [targetLights[j], targetLights[i]];
    }
    
    const positions = calculateWirePositions(wireCount);
    
    for (let i = 0; i < wireCount; i++) {
        wires.push({
            id: i + 1,
            color: colors[i],
            startPos: positions.leftSide[i],
            targetPos: positions.rightSide[targetLights[i]],
            targetLight: targetLights[i] + 1,
            targetX: 670, // Relative to drag start
            targetY: positions.rightSide[targetLights[i]].y - positions.leftSide[i].y
        });
    }
    
    return wires;
}

// Vue component
const MxFixingWiring = {
    name: "Mx_Fixing_Wiring_Enhanced",
    data() {
        return {
            NuiOpen: false,
            size_game: "width: 90vmin;",
            position_nui: "left: 50%; top: 50%; transform: translate(-50%, -50%) scale(1);",
            name_resource: "",
            sound_name: "",
            wire_count: 4,
            colors: "auto",
            snap_radius: 25,
            wires: [],
            completedWires: [],
            gameWidth: 907,
            gameHeight: 907
        };
    },
    
    computed: {
        wirePositions() {
            return calculateWirePositions(this.wire_count, this.gameWidth, this.gameHeight);
        },
        
        wireColors() {
            return getColors(this.colors, this.wire_count);
        }
    },
    
    created() {
        window.addEventListener("message", this.receiveLua);
        window.addEventListener("keydown", this.keyPress);
    },
    
    destroyed() {
        window.removeEventListener("message", this.receiveLua);
        window.removeEventListener("keydown", this.keyPress);
    },
    
    methods: {
        SendToClient(endpoint, data) {
            axios.post(`https://${this.name_resource}/${endpoint}`, data)
                .catch(error => console.log(`An error occurred in: ${endpoint} , ${error}`));
        },
        
        keyPress(event) {
            const key = event.key;
            if (key === "Escape" || key === "Backspace") {
                this.SendToClient("CloseNui", {});
            }
        },
        
        receiveLua(event) {
            if (event && event.data) {
                const data = event.data;
                if (data.ui === "ui") {
                    this.NuiOpen = data.NuiOpen;
                    if (data.NuiOpen) {
                        this.position_nui = `left: ${data.x}; top: ${data.y}; transform: translate(-${data.x}, -${data.y}) scale(${data.scale});`;
                        this.size_game = `width: ${data.size_game};`;
                        this.name_resource = data.name_resource;
                        this.sound_name = data.sound_name;
                        this.wire_count = data.wire_count || 4;
                        this.colors = data.colors || "auto";
                        this.snap_radius = data.snap_radius || 25;
                        
                        gsap.registerPlugin(Draggable);
                        
                        setTimeout(() => {
                            this.initializeGame();
                        }, 100);
                    }
                }
            }
        },
        
        initializeGame() {
            this.wires = generateRandomConfiguration(this.wire_count, this.wireColors);
            this.completedWires = new Array(this.wire_count).fill(false);
            
            setTimeout(() => {
                this.setupDraggables();
            }, 200);
        },
        
        setupDraggables() {
            const self = this;

            // Setup draggable for each wire
            for (let i = 0; i < this.wire_count; i++) {
                const wireId = i + 1;
                const wire = this.wires[i];

                new Draggable(`.drag-${wireId}`, {
                    onDrag: function() {
                        // Direct update for smooth dragging
                        self.updateLine(wireId, this.x + 120, this.y + wire.startPos.y);
                    },

                    onRelease: function() {
                        const isCorrect = (this.x === wire.targetX && this.y === wire.targetY);

                        if (isCorrect) {
                            self.toggleLight(wire.targetLight, true);
                            self.completedWires[i] = true;
                            self.checkCompletion();
                        } else {
                            self.resetWire(wireId, wire);
                            self.toggleLight(wire.targetLight, false);
                            self.completedWires[i] = false;
                        }
                    },

                    liveSnap: {
                        points: [{x: wire.targetX, y: wire.targetY}],
                        radius: self.snap_radius
                    }
                });
            }
        },

        updateLine(wireId, x2, y2) {
            // Cache line elements for better performance
            if (!this.lineElementsCache) {
                this.lineElementsCache = {};
            }

            if (!this.lineElementsCache[wireId]) {
                this.lineElementsCache[wireId] = document.querySelector(`.line-${wireId}`);
            }

            const lineElement = this.lineElementsCache[wireId];
            if (lineElement) {
                // Direct DOM manipulation is faster than GSAP for simple attribute changes during drag
                lineElement.setAttributeNS(null, 'x2', x2);
                lineElement.setAttributeNS(null, 'y2', y2);
            }
        },

        toggleLight(lightId, isOn) {
            gsap.to(`.light-${lightId}`, {
                opacity: isOn ? 1 : 0,
                duration: 0.3
            });
        },

        resetWire(wireId, wire) {
            gsap.to(`.drag-${wireId}`, {
                duration: 0.3,
                ease: "power2.out",
                x: 0,
                y: 0
            });

            gsap.to(`.line-${wireId}`, {
                duration: 0.3,
                ease: "power2.out",
                attr: {
                    x2: wire.startPos.x + 60,
                    y2: wire.startPos.y
                }
            });
        },

        checkCompletion() {
            const allCompleted = this.completedWires.every(completed => completed);

            if (allCompleted) {
                // Play success sound
                if (this.sound_name) {
                    const audio = new Audio(`sound/${this.sound_name}`);
                    audio.play().catch(() => {}); // Ignore audio errors
                }

                // Reset all wires after delay and complete
                setTimeout(() => {
                    for (let i = 0; i < this.wire_count; i++) {
                        this.resetWire(i + 1, this.wires[i]);
                        this.toggleLight(i + 1, false);
                    }
                    this.completedWires.fill(false);
                    this.SendToClient("electric_circuit_completed", {});
                }, 2000);
            }
        },

        generateSVGElements() {
            // Generate SVG elements dynamically based on wire count
            const elements = [];

            for (let i = 0; i < this.wire_count; i++) {
                const wire = this.wires[i];
                const wireId = i + 1;

                // Add wire drag element
                elements.push({
                    type: 'rect',
                    class: `drag drag-${wireId}`,
                    attrs: {
                        x: wire.startPos.x,
                        y: wire.startPos.y,
                        width: wire.startPos.width,
                        height: wire.startPos.height,
                        fill: wire.color
                    }
                });

                // Add line element
                elements.push({
                    type: 'line',
                    class: `line line-${wireId}`,
                    attrs: {
                        x1: wire.startPos.x + 30,
                        y1: wire.startPos.y + 20,
                        x2: wire.startPos.x + 30,
                        y2: wire.startPos.y + 20,
                        stroke: wire.color,
                        'stroke-width': 3
                    }
                });

                // Add target connection area
                elements.push({
                    type: 'rect',
                    class: `target target-${wireId}`,
                    attrs: {
                        x: wire.targetPos.x,
                        y: wire.targetPos.y,
                        width: wire.targetPos.width,
                        height: wire.targetPos.height,
                        fill: wire.color,
                        opacity: 0.3
                    }
                });

                // Add light element
                elements.push({
                    type: 'rect',
                    class: `light light-${wireId}`,
                    attrs: {
                        x: this.gameWidth - 60,
                        y: wire.targetPos.y - 5,
                        width: 50,
                        height: 24,
                        fill: '#ffff00',
                        opacity: 0
                    }
                });
            }

            return elements;
        }
    }
};
    }
};

export default MxFixingWiring;
